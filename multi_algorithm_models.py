#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多算法模型实验
实现XGBoost、神经网络、集成方法等多种算法
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, VotingRegressor
from sklearn.neural_network import MLPRegressor
from sklearn.linear_model import LinearRegression, Ridge, Lasso
from sklearn.svm import SVR
from sklearn.model_selection import GridSearchCV, cross_val_score, TimeSeriesSplit
from sklearn.metrics import mean_squared_error, mean_absolute_error
from sklearn.preprocessing import StandardScaler
import xgboost as xgb
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class MultiAlgorithmModels:
    def __init__(self):
        self.models = {}
        self.results = {}
        self.scaler = StandardScaler()
        
    def load_enhanced_features(self):
        """加载增强特征数据"""
        from advanced_feature_engineering import AdvancedFeatureEngineer
        engineer = AdvancedFeatureEngineer()
        results = engineer.run_advanced_feature_engineering()
        
        self.train_df = results['train_df']
        self.test_df = results['test_df']
        self.selected_features = results['selected_features']
        self.pca_features = results['pca_features']
        
        print(f"加载数据完成 - 训练集：{len(self.train_df)}期，测试集：{len(self.test_df)}期")
        
    def prepare_data(self, target_col, feature_type='selected'):
        """准备训练数据"""
        if feature_type == 'selected':
            # 使用特征选择后的特征
            features = self.selected_features[target_col]
        elif feature_type == 'pca':
            # 使用PCA特征
            train_pca, test_pca = self.pca_features
            X_train = train_pca.values
            X_test = test_pca.values
            y_train = self.train_df[target_col].values
            y_test = self.test_df[target_col].values
            return X_train, X_test, y_train, y_test
        else:
            # 使用所有特征
            exclude_cols = ['期号', '数字1', '数字2', '数字3', '数字4', '数字5', '数字6', '特码']
            features = [col for col in self.train_df.columns if col not in exclude_cols]
        
        X_train = self.train_df[features].fillna(0).values
        X_test = self.test_df[features].fillna(0).values
        y_train = self.train_df[target_col].values
        y_test = self.test_df[target_col].values
        
        return X_train, X_test, y_train, y_test
    
    def train_xgboost(self, target_col):
        """训练XGBoost模型"""
        print(f"\n=== 训练XGBoost模型 - {target_col} ===")
        
        X_train, X_test, y_train, y_test = self.prepare_data(target_col, 'selected')
        
        # XGBoost参数
        xgb_params = {
            'n_estimators': [100, 200],
            'max_depth': [3, 5, 7],
            'learning_rate': [0.01, 0.1, 0.2],
            'subsample': [0.8, 1.0]
        }
        
        xgb_model = xgb.XGBRegressor(random_state=42, n_jobs=-1)
        
        # 时间序列交叉验证
        tscv = TimeSeriesSplit(n_splits=3)
        
        # 网格搜索
        grid_search = GridSearchCV(
            xgb_model, xgb_params, cv=tscv, 
            scoring='neg_mean_squared_error', n_jobs=-1
        )
        
        grid_search.fit(X_train, y_train)
        best_xgb = grid_search.best_estimator_
        
        # 预测
        y_pred_train = best_xgb.predict(X_train)
        y_pred_test = best_xgb.predict(X_test)
        
        # 评估
        train_mse = mean_squared_error(y_train, y_pred_train)
        test_mse = mean_squared_error(y_test, y_pred_test)
        train_mae = mean_absolute_error(y_train, y_pred_train)
        test_mae = mean_absolute_error(y_test, y_pred_test)
        
        print(f"最佳参数: {grid_search.best_params_}")
        print(f"训练MSE: {train_mse:.2f}, 测试MSE: {test_mse:.2f}")
        print(f"训练MAE: {train_mae:.2f}, 测试MAE: {test_mae:.2f}")
        
        return {
            'model': best_xgb,
            'train_mse': train_mse,
            'test_mse': test_mse,
            'train_mae': train_mae,
            'test_mae': test_mae,
            'predictions': y_pred_test
        }
    
    def train_neural_network(self, target_col):
        """训练神经网络模型"""
        print(f"\n=== 训练神经网络模型 - {target_col} ===")
        
        X_train, X_test, y_train, y_test = self.prepare_data(target_col, 'selected')
        
        # 标准化数据
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # 神经网络参数
        nn_params = {
            'hidden_layer_sizes': [(50,), (100,), (50, 25), (100, 50)],
            'activation': ['relu', 'tanh'],
            'alpha': [0.001, 0.01, 0.1],
            'learning_rate': ['constant', 'adaptive']
        }
        
        nn_model = MLPRegressor(max_iter=1000, random_state=42)
        
        # 时间序列交叉验证
        tscv = TimeSeriesSplit(n_splits=3)
        
        # 网格搜索
        grid_search = GridSearchCV(
            nn_model, nn_params, cv=tscv, 
            scoring='neg_mean_squared_error', n_jobs=-1
        )
        
        grid_search.fit(X_train_scaled, y_train)
        best_nn = grid_search.best_estimator_
        
        # 预测
        y_pred_train = best_nn.predict(X_train_scaled)
        y_pred_test = best_nn.predict(X_test_scaled)
        
        # 评估
        train_mse = mean_squared_error(y_train, y_pred_train)
        test_mse = mean_squared_error(y_test, y_pred_test)
        train_mae = mean_absolute_error(y_train, y_pred_train)
        test_mae = mean_absolute_error(y_test, y_pred_test)
        
        print(f"最佳参数: {grid_search.best_params_}")
        print(f"训练MSE: {train_mse:.2f}, 测试MSE: {test_mse:.2f}")
        print(f"训练MAE: {train_mae:.2f}, 测试MAE: {test_mae:.2f}")
        
        return {
            'model': best_nn,
            'train_mse': train_mse,
            'test_mse': test_mse,
            'train_mae': train_mae,
            'test_mae': test_mae,
            'predictions': y_pred_test
        }
    
    def train_ensemble_methods(self, target_col):
        """训练集成方法"""
        print(f"\n=== 训练集成方法 - {target_col} ===")
        
        X_train, X_test, y_train, y_test = self.prepare_data(target_col, 'selected')
        
        # 基础模型
        rf = RandomForestRegressor(n_estimators=100, max_depth=10, random_state=42)
        gb = GradientBoostingRegressor(n_estimators=100, max_depth=5, random_state=42)
        ridge = Ridge(alpha=1.0)
        
        # 投票回归器
        voting_regressor = VotingRegressor([
            ('rf', rf),
            ('gb', gb),
            ('ridge', ridge)
        ])
        
        # 训练模型
        voting_regressor.fit(X_train, y_train)
        
        # 预测
        y_pred_train = voting_regressor.predict(X_train)
        y_pred_test = voting_regressor.predict(X_test)
        
        # 评估
        train_mse = mean_squared_error(y_train, y_pred_train)
        test_mse = mean_squared_error(y_test, y_pred_test)
        train_mae = mean_absolute_error(y_train, y_pred_train)
        test_mae = mean_absolute_error(y_test, y_pred_test)
        
        print(f"训练MSE: {train_mse:.2f}, 测试MSE: {test_mse:.2f}")
        print(f"训练MAE: {train_mae:.2f}, 测试MAE: {test_mae:.2f}")
        
        return {
            'model': voting_regressor,
            'train_mse': train_mse,
            'test_mse': test_mse,
            'train_mae': train_mae,
            'test_mae': test_mae,
            'predictions': y_pred_test
        }
    
    def train_gradient_boosting(self, target_col):
        """训练梯度提升模型"""
        print(f"\n=== 训练梯度提升模型 - {target_col} ===")
        
        X_train, X_test, y_train, y_test = self.prepare_data(target_col, 'selected')
        
        # 梯度提升参数
        gb_params = {
            'n_estimators': [100, 200],
            'max_depth': [3, 5, 7],
            'learning_rate': [0.01, 0.1, 0.2],
            'subsample': [0.8, 1.0]
        }
        
        gb_model = GradientBoostingRegressor(random_state=42)
        
        # 时间序列交叉验证
        tscv = TimeSeriesSplit(n_splits=3)
        
        # 网格搜索
        grid_search = GridSearchCV(
            gb_model, gb_params, cv=tscv, 
            scoring='neg_mean_squared_error', n_jobs=-1
        )
        
        grid_search.fit(X_train, y_train)
        best_gb = grid_search.best_estimator_
        
        # 预测
        y_pred_train = best_gb.predict(X_train)
        y_pred_test = best_gb.predict(X_test)
        
        # 评估
        train_mse = mean_squared_error(y_train, y_pred_train)
        test_mse = mean_squared_error(y_test, y_pred_test)
        train_mae = mean_absolute_error(y_train, y_pred_train)
        test_mae = mean_absolute_error(y_test, y_pred_test)
        
        print(f"最佳参数: {grid_search.best_params_}")
        print(f"训练MSE: {train_mse:.2f}, 测试MSE: {test_mse:.2f}")
        print(f"训练MAE: {train_mae:.2f}, 测试MAE: {test_mae:.2f}")
        
        return {
            'model': best_gb,
            'train_mse': train_mse,
            'test_mse': test_mse,
            'train_mae': train_mae,
            'test_mae': test_mae,
            'predictions': y_pred_test
        }
    
    def evaluate_lottery_prediction_accuracy(self, predictions_dict):
        """评估彩票预测准确性"""
        print("\n=== 彩票预测准确性评估 ===")
        
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        # 获取实际值
        actual_values = self.test_df[target_cols].values
        
        results = {}
        
        for model_name, model_predictions in predictions_dict.items():
            print(f"\n{model_name} 预测结果：")
            
            # 将预测值四舍五入到最近的整数
            predicted_values = np.array([
                np.round(model_predictions[col]).astype(int) 
                for col in target_cols
            ]).T
            
            # 确保预测值在有效范围内
            predicted_values = np.clip(predicted_values, 1, 49)
            
            # 计算每期预测正确的数字个数
            correct_per_period = []
            for i in range(len(actual_values)):
                actual_set = set(actual_values[i])
                predicted_set = set(predicted_values[i])
                correct_count = len(actual_set.intersection(predicted_set))
                correct_per_period.append(correct_count)
            
            avg_correct = np.mean(correct_per_period)
            
            print(f"平均每期预测正确数字个数: {avg_correct:.3f}")
            print(f"最好单期预测: {max(correct_per_period)} 个数字")
            print(f"预测正确率分布: {np.bincount(correct_per_period)}")
            
            results[model_name] = {
                'avg_correct': avg_correct,
                'max_correct': max(correct_per_period),
                'correct_distribution': correct_per_period
            }
        
        return results
    
    def run_multi_algorithm_experiment(self):
        """运行多算法实验"""
        print("开始多算法模型实验")
        print("=" * 60)
        
        # 加载数据
        self.load_enhanced_features()
        
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        all_predictions = {}
        
        for target_col in target_cols:
            print(f"\n{'='*20} {target_col} {'='*20}")
            
            # 训练不同算法
            xgb_result = self.train_xgboost(target_col)
            nn_result = self.train_neural_network(target_col)
            ensemble_result = self.train_ensemble_methods(target_col)
            gb_result = self.train_gradient_boosting(target_col)
            
            # 保存结果
            self.results[target_col] = {
                'XGBoost': xgb_result,
                'NeuralNetwork': nn_result,
                'Ensemble': ensemble_result,
                'GradientBoosting': gb_result
            }
            
            # 收集预测结果
            for model_name, result in self.results[target_col].items():
                if model_name not in all_predictions:
                    all_predictions[model_name] = {}
                all_predictions[model_name][target_col] = result['predictions']
        
        # 评估预测准确性
        accuracy_results = self.evaluate_lottery_prediction_accuracy(all_predictions)
        
        print("\n" + "=" * 60)
        print("多算法实验完成！")
        
        return {
            'model_results': self.results,
            'accuracy_results': accuracy_results,
            'predictions': all_predictions
        }

if __name__ == "__main__":
    experiment = MultiAlgorithmModels()
    results = experiment.run_multi_algorithm_experiment()
