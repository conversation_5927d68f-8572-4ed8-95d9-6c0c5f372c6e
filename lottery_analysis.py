#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
彩票数据分析项目
包含数据清洗、探索性数据分析、特征工程、模型训练等功能
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import re
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class LotteryAnalyzer:
    def __init__(self, data_file='数据集.txt'):
        self.data_file = data_file
        self.df = None
        self.train_df = None
        self.test_df = None
        
    def load_and_clean_data(self):
        """步骤1：数据准备与清洗"""
        print("=== 步骤1：数据准备与清洗 ===")
        
        # 读取原始数据
        with open(self.data_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        data = []
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 使用正则表达式解析每行数据
            # 格式：160 期：23，42，32，46，30，40，特码是 11
            match = re.match(r'(\d+)\s*期：(.+?)，特码是\s*(\d+)', line)
            if match:
                period = int(match.group(1))
                numbers_str = match.group(2)
                special_code = int(match.group(3))
                
                # 解析6个正常数字
                numbers = [int(x.strip()) for x in numbers_str.split('，')]
                
                if len(numbers) == 6:
                    row = [period] + numbers + [special_code]
                    data.append(row)
        
        # 创建DataFrame
        columns = ['期号', '数字1', '数字2', '数字3', '数字4', '数字5', '数字6', '特码']
        self.df = pd.DataFrame(data, columns=columns)
        
        # 按期号排序（从小到大）
        self.df = self.df.sort_values('期号').reset_index(drop=True)
        
        print(f"数据加载完成，共{len(self.df)}期数据")
        print("数据预览：")
        print(self.df.head())
        print("\n数据基本信息：")
        print(self.df.info())
        
        # 保存为CSV文件
        self.df.to_csv('lottery_data.csv', index=False, encoding='utf-8-sig')
        print("\n数据已保存为 lottery_data.csv")
        
        return self.df
    
    def exploratory_data_analysis(self):
        """步骤2：探索性数据分析(EDA)"""
        print("\n=== 步骤2：探索性数据分析(EDA) ===")
        
        if self.df is None:
            print("请先执行数据加载")
            return
        
        # 创建所有数字的列表（包含特码）
        all_numbers = []
        for col in ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6', '特码']:
            all_numbers.extend(self.df[col].tolist())
        
        # 1. 冷热号分析
        print("1. 冷热号分析")
        number_counts = Counter(all_numbers)
        hot_numbers = number_counts.most_common(10)
        cold_numbers = number_counts.most_common()[-10:]
        
        print("热门号码（前10）：", hot_numbers)
        print("冷门号码（后10）：", cold_numbers)
        
        # 2. 奇偶比分析
        print("\n2. 奇偶比分析")
        odd_count = sum(1 for num in all_numbers if num % 2 == 1)
        even_count = len(all_numbers) - odd_count
        print(f"奇数：{odd_count}个 ({odd_count/len(all_numbers)*100:.1f}%)")
        print(f"偶数：{even_count}个 ({even_count/len(all_numbers)*100:.1f}%)")
        
        # 3. 大小比分析（以25为界）
        print("\n3. 大小比分析（以25为界）")
        big_count = sum(1 for num in all_numbers if num > 25)
        small_count = len(all_numbers) - big_count
        print(f"大数(>25)：{big_count}个 ({big_count/len(all_numbers)*100:.1f}%)")
        print(f"小数(≤25)：{small_count}个 ({small_count/len(all_numbers)*100:.1f}%)")
        
        # 4. 和值分布分析
        print("\n4. 和值分布分析")
        sum_values = []
        for _, row in self.df.iterrows():
            six_numbers_sum = sum([row['数字1'], row['数字2'], row['数字3'], 
                                 row['数字4'], row['数字5'], row['数字6']])
            sum_values.append(six_numbers_sum)
        
        print(f"和值范围：{min(sum_values)} - {max(sum_values)}")
        print(f"和值平均值：{np.mean(sum_values):.2f}")
        print(f"和值标准差：{np.std(sum_values):.2f}")
        
        # 5. 可视化分析
        self._create_visualizations(number_counts, sum_values)
        
        return {
            'number_counts': number_counts,
            'sum_values': sum_values,
            'hot_numbers': hot_numbers,
            'cold_numbers': cold_numbers
        }
    
    def _create_visualizations(self, number_counts, sum_values):
        """创建可视化图表"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 号码出现频率分布
        numbers = list(range(1, 50))
        frequencies = [number_counts.get(num, 0) for num in numbers]
        
        axes[0, 0].bar(numbers, frequencies, alpha=0.7)
        axes[0, 0].set_title('号码出现频率分布')
        axes[0, 0].set_xlabel('号码')
        axes[0, 0].set_ylabel('出现次数')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 和值分布直方图
        axes[0, 1].hist(sum_values, bins=20, alpha=0.7, edgecolor='black')
        axes[0, 1].set_title('和值分布直方图')
        axes[0, 1].set_xlabel('和值')
        axes[0, 1].set_ylabel('频次')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 奇偶数分布
        odd_count = sum(1 for num in number_counts.keys() if num % 2 == 1)
        even_count = len(number_counts) - odd_count
        axes[1, 0].pie([odd_count, even_count], labels=['奇数', '偶数'], autopct='%1.1f%%')
        axes[1, 0].set_title('奇偶数分布')
        
        # 4. 大小数分布
        big_count = sum(1 for num in number_counts.keys() if num > 25)
        small_count = len(number_counts) - big_count
        axes[1, 1].pie([big_count, small_count], labels=['大数(>25)', '小数(≤25)'], autopct='%1.1f%%')
        axes[1, 1].set_title('大小数分布')
        
        plt.tight_layout()
        plt.savefig('lottery_eda_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("可视化图表已保存为 lottery_eda_analysis.png")

    def feature_engineering(self):
        """步骤3：特征工程"""
        print("\n=== 步骤3：特征工程 ===")

        if self.df is None:
            print("请先执行数据加载")
            return

        # 创建特征DataFrame
        features_df = self.df.copy()

        # 1. 滞后特征 (Lag Features)
        print("1. 创建滞后特征...")
        lag_periods = [1, 2, 3, 5, 10]  # 前1期、前2期、前3期、前5期、前10期

        for lag in lag_periods:
            for col in ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6', '特码']:
                features_df[f'{col}_lag{lag}'] = features_df[col].shift(lag)

        # 2. 滑动窗口统计特征 (Rolling Window Features)
        print("2. 创建滑动窗口统计特征...")
        window_sizes = [5, 10, 20]

        # 计算和值
        features_df['和值'] = features_df[['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']].sum(axis=1)

        for window in window_sizes:
            # 和值的滑动统计
            features_df[f'和值_mean_{window}'] = features_df['和值'].rolling(window=window).mean()
            features_df[f'和值_std_{window}'] = features_df['和值'].rolling(window=window).std()

            # 奇偶比的滑动统计
            odd_count = features_df[['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']].apply(
                lambda x: sum(num % 2 for num in x), axis=1)
            features_df[f'奇数个数_mean_{window}'] = odd_count.rolling(window=window).mean()

            # 大小比的滑动统计（以25为界）
            big_count = features_df[['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']].apply(
                lambda x: sum(num > 25 for num in x), axis=1)
            features_df[f'大数个数_mean_{window}'] = big_count.rolling(window=window).mean()

        # 3. 间隔特征 (Interval Features)
        print("3. 创建间隔特征...")
        # 计算每个数字距离上一次出现的时间间隔
        for num in range(1, 50):  # 1-49的数字
            intervals = []
            last_seen = -1

            for i, (_, row) in enumerate(features_df.iterrows()):
                # 检查这个数字是否在当前期出现
                current_numbers = [row['数字1'], row['数字2'], row['数字3'],
                                 row['数字4'], row['数字5'], row['数字6'], row['特码']]

                if num in current_numbers:
                    if last_seen == -1:
                        intervals.append(0)  # 第一次出现
                    else:
                        intervals.append(i - last_seen)
                    last_seen = i
                else:
                    if last_seen == -1:
                        intervals.append(i + 1)  # 从未出现过
                    else:
                        intervals.append(i - last_seen)

            features_df[f'数字{num}_间隔'] = intervals

        # 4. 组合特征
        print("4. 创建组合特征...")
        # 连号个数（相邻数字的个数）
        consecutive_counts = []
        for _, row in features_df.iterrows():
            numbers = sorted([row['数字1'], row['数字2'], row['数字3'],
                            row['数字4'], row['数字5'], row['数字6']])
            consecutive = 0
            for i in range(len(numbers)-1):
                if numbers[i+1] - numbers[i] == 1:
                    consecutive += 1
            consecutive_counts.append(consecutive)

        features_df['连号个数'] = consecutive_counts

        # 重复号个数（与上期相同的号码个数）
        repeat_counts = []
        for i, (_, row) in enumerate(features_df.iterrows()):
            if i == 0:
                repeat_counts.append(0)
            else:
                current_numbers = set([row['数字1'], row['数字2'], row['数字3'],
                                     row['数字4'], row['数字5'], row['数字6']])
                prev_row = features_df.iloc[i-1]
                prev_numbers = set([prev_row['数字1'], prev_row['数字2'], prev_row['数字3'],
                                  prev_row['数字4'], prev_row['数字5'], prev_row['数字6']])
                repeat_count = len(current_numbers.intersection(prev_numbers))
                repeat_counts.append(repeat_count)

        features_df['重复号个数'] = repeat_counts

        self.features_df = features_df
        print(f"特征工程完成，共创建了{len(features_df.columns)}个特征")
        print("特征列表：", list(features_df.columns))

        return features_df

    def split_dataset(self):
        """步骤4：数据集划分"""
        print("\n=== 步骤4：数据集划分 ===")

        if self.features_df is None:
            print("请先执行特征工程")
            return

        # 使用第1-140期作为训练集，第141-160期作为测试集
        train_mask = self.features_df['期号'] <= 140
        test_mask = self.features_df['期号'] > 140

        self.train_df = self.features_df[train_mask].copy()
        self.test_df = self.features_df[test_mask].copy()

        print(f"训练集：{len(self.train_df)}期 (第1-140期)")
        print(f"测试集：{len(self.test_df)}期 (第141-160期)")

        # 移除包含NaN的行（由于滞后特征和滑动窗口特征产生的）
        train_before = len(self.train_df)
        test_before = len(self.test_df)

        self.train_df = self.train_df.dropna()
        self.test_df = self.test_df.dropna()

        print(f"移除NaN后 - 训练集：{len(self.train_df)}期，测试集：{len(self.test_df)}期")

        return self.train_df, self.test_df

if __name__ == "__main__":
    # 创建分析器实例
    analyzer = LotteryAnalyzer()

    # 执行数据加载和清洗
    df = analyzer.load_and_clean_data()

    # 执行探索性数据分析
    eda_results = analyzer.exploratory_data_analysis()

    # 执行特征工程
    features_df = analyzer.feature_engineering()

    # 数据集划分
    train_df, test_df = analyzer.split_dataset()
