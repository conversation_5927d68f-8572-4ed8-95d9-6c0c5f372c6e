#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第一阶段：高级模型集成优化
实施动态权重调整、增加深度学习模型、优化投票策略、实施堆叠集成
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, VotingRegressor
from sklearn.neural_network import MLPRegressor
from sklearn.linear_model import Ridge, ElasticNet
from sklearn.svm import SVR
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import TimeSeriesSplit, cross_val_score
from sklearn.metrics import mean_squared_error, mean_absolute_error
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout, BatchNormalization
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping
import xgboost as xgb
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class AdvancedEnsembleOptimization:
    def __init__(self):
        self.enhanced_df = None
        self.selected_features = None
        self.base_models = {}
        self.ensemble_models = {}
        self.dynamic_weights = {}
        self.performance_history = {}
        
    def load_enhanced_data(self):
        """加载增强特征数据"""
        print("=== 加载增强特征数据 ===")
        
        # 加载特征工程结果
        self.enhanced_df = pd.read_csv('advanced_enhanced_features.csv')
        
        # 加载特征选择结果（需要重新运行特征选择）
        from advanced_feature_engineering_optimization import AdvancedFeatureEngineering
        engineer = AdvancedFeatureEngineering()
        engineer.enhanced_df = self.enhanced_df
        engineer.optimize_feature_selection()
        self.selected_features = engineer.selected_features
        
        print(f"增强数据加载完成：{len(self.enhanced_df)}期，{len(self.enhanced_df.columns)}个特征")
        return True
    
    def create_advanced_base_models(self):
        """创建高级基础模型"""
        print("\n=== 创建高级基础模型 ===")
        
        # 1. 传统机器学习模型
        traditional_models = {
            'RandomForest': RandomForestRegressor(
                n_estimators=200,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                n_jobs=-1
            ),
            'XGBoost': xgb.XGBRegressor(
                n_estimators=200,
                max_depth=8,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                n_jobs=-1
            ),
            'GradientBoosting': GradientBoostingRegressor(
                n_estimators=200,
                max_depth=8,
                learning_rate=0.1,
                subsample=0.8,
                random_state=42
            ),
            'ElasticNet': ElasticNet(
                alpha=0.1,
                l1_ratio=0.5,
                random_state=42
            ),
            'SVR': SVR(
                kernel='rbf',
                C=1.0,
                gamma='scale'
            )
        }
        
        # 2. 神经网络模型
        neural_models = {
            'MLP_Small': MLPRegressor(
                hidden_layer_sizes=(100, 50),
                activation='relu',
                solver='adam',
                alpha=0.01,
                learning_rate='adaptive',
                max_iter=500,
                random_state=42
            ),
            'MLP_Large': MLPRegressor(
                hidden_layer_sizes=(200, 100, 50),
                activation='relu',
                solver='adam',
                alpha=0.01,
                learning_rate='adaptive',
                max_iter=500,
                random_state=42
            )
        }
        
        self.base_models = {**traditional_models, **neural_models}
        print(f"创建了 {len(self.base_models)} 个基础模型")
        
    def create_lstm_model(self, input_shape):
        """创建LSTM深度学习模型"""
        model = Sequential([
            LSTM(128, return_sequences=True, input_shape=input_shape),
            Dropout(0.2),
            BatchNormalization(),
            LSTM(64, return_sequences=False),
            Dropout(0.2),
            BatchNormalization(),
            Dense(32, activation='relu'),
            Dropout(0.1),
            Dense(1, activation='linear')
        ])
        
        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='mse',
            metrics=['mae']
        )
        
        return model
    
    def create_transformer_model(self, input_shape):
        """创建Transformer模型"""
        from tensorflow.keras.layers import MultiHeadAttention, LayerNormalization, GlobalAveragePooling1D
        
        inputs = tf.keras.Input(shape=input_shape)
        
        # Multi-head attention
        attention_output = MultiHeadAttention(
            num_heads=8, key_dim=64
        )(inputs, inputs)
        
        # Add & Norm
        attention_output = LayerNormalization()(inputs + attention_output)
        
        # Feed Forward
        ffn_output = Dense(128, activation='relu')(attention_output)
        ffn_output = Dense(input_shape[-1])(ffn_output)
        
        # Add & Norm
        ffn_output = LayerNormalization()(attention_output + ffn_output)
        
        # Global pooling and output
        pooled = GlobalAveragePooling1D()(ffn_output)
        outputs = Dense(1, activation='linear')(pooled)
        
        model = tf.keras.Model(inputs, outputs)
        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='mse',
            metrics=['mae']
        )
        
        return model
    
    def train_base_models(self):
        """训练基础模型"""
        print("\n=== 训练基础模型 ===")
        
        df = self.enhanced_df.dropna().reset_index(drop=True)
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        # 数据分割
        train_df = df[df['期号'] <= 150].copy()
        test_df = df[df['期号'] >= 151].copy()
        
        trained_models = {}
        
        for target_col in target_cols:
            print(f"\n训练 {target_col} 的模型...")
            
            # 准备数据
            selected_features = self.selected_features[target_col]
            X_train = train_df[selected_features].fillna(0)
            y_train = train_df[target_col]
            X_test = test_df[selected_features].fillna(0)
            y_test = test_df[target_col]
            
            # 标准化
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            target_models = {}
            
            # 训练传统模型
            for model_name, model in self.base_models.items():
                print(f"  训练 {model_name}...")
                
                if 'MLP' in model_name:
                    # 神经网络使用标准化数据
                    model.fit(X_train_scaled, y_train)
                    train_pred = model.predict(X_train_scaled)
                    test_pred = model.predict(X_test_scaled)
                else:
                    # 其他模型使用原始数据
                    model.fit(X_train, y_train)
                    train_pred = model.predict(X_train)
                    test_pred = model.predict(X_test)
                
                # 计算性能
                train_mse = mean_squared_error(y_train, train_pred)
                test_mse = mean_squared_error(y_test, test_pred)
                
                target_models[model_name] = {
                    'model': model,
                    'scaler': scaler if 'MLP' in model_name else None,
                    'train_mse': train_mse,
                    'test_mse': test_mse,
                    'train_pred': train_pred,
                    'test_pred': test_pred
                }
                
                print(f"    训练MSE: {train_mse:.4f}, 测试MSE: {test_mse:.4f}")
            
            # 训练LSTM模型
            print(f"  训练 LSTM...")
            
            # 为LSTM准备序列数据
            sequence_length = 10
            X_train_seq, y_train_seq = self.create_sequences(X_train_scaled, y_train, sequence_length)
            X_test_seq, y_test_seq = self.create_sequences(X_test_scaled, y_test, sequence_length)
            
            if len(X_train_seq) > 0:
                lstm_model = self.create_lstm_model((sequence_length, X_train_scaled.shape[1]))
                
                early_stopping = EarlyStopping(
                    monitor='val_loss',
                    patience=20,
                    restore_best_weights=True
                )
                
                history = lstm_model.fit(
                    X_train_seq, y_train_seq,
                    validation_split=0.2,
                    epochs=100,
                    batch_size=32,
                    callbacks=[early_stopping],
                    verbose=0
                )
                
                # 预测
                train_pred_lstm = lstm_model.predict(X_train_seq, verbose=0).flatten()
                test_pred_lstm = lstm_model.predict(X_test_seq, verbose=0).flatten()
                
                train_mse_lstm = mean_squared_error(y_train_seq, train_pred_lstm)
                test_mse_lstm = mean_squared_error(y_test_seq, test_pred_lstm)
                
                target_models['LSTM'] = {
                    'model': lstm_model,
                    'scaler': scaler,
                    'train_mse': train_mse_lstm,
                    'test_mse': test_mse_lstm,
                    'train_pred': train_pred_lstm,
                    'test_pred': test_pred_lstm,
                    'sequence_length': sequence_length
                }
                
                print(f"    训练MSE: {train_mse_lstm:.4f}, 测试MSE: {test_mse_lstm:.4f}")
            
            trained_models[target_col] = target_models
        
        self.trained_models = trained_models
        return trained_models
    
    def create_sequences(self, X, y, sequence_length):
        """为LSTM创建序列数据"""
        X_seq, y_seq = [], []
        
        for i in range(sequence_length, len(X)):
            X_seq.append(X[i-sequence_length:i])
            y_seq.append(y.iloc[i] if hasattr(y, 'iloc') else y[i])
        
        return np.array(X_seq), np.array(y_seq)
    
    def implement_dynamic_weighting(self):
        """实施动态权重调整"""
        print("\n=== 实施动态权重调整 ===")
        
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        for target_col in target_cols:
            print(f"\n为 {target_col} 计算动态权重...")
            
            models = self.trained_models[target_col]
            
            # 基于测试性能计算权重
            test_mses = []
            model_names = []
            
            for model_name, model_info in models.items():
                test_mses.append(model_info['test_mse'])
                model_names.append(model_name)
            
            # 转换MSE为权重（MSE越小，权重越大）
            test_mses = np.array(test_mses)
            # 使用倒数权重，并进行softmax归一化
            inverse_mses = 1.0 / (test_mses + 1e-8)
            weights = inverse_mses / np.sum(inverse_mses)
            
            # 基于近期表现调整权重
            recent_weights = self.calculate_recent_performance_weights(target_col, models)
            
            # 组合权重（70%基于整体性能，30%基于近期表现）
            final_weights = 0.7 * weights + 0.3 * recent_weights
            final_weights = final_weights / np.sum(final_weights)  # 重新归一化
            
            self.dynamic_weights[target_col] = dict(zip(model_names, final_weights))
            
            print(f"  动态权重分配：")
            for model_name, weight in self.dynamic_weights[target_col].items():
                print(f"    {model_name}: {weight:.3f}")
    
    def calculate_recent_performance_weights(self, target_col, models):
        """计算基于近期表现的权重"""
        # 使用最后10期的表现来计算权重
        recent_period = 10
        
        recent_mses = []
        for model_name, model_info in models.items():
            test_pred = model_info['test_pred']
            if len(test_pred) >= recent_period:
                # 计算最后10期的MSE
                recent_pred = test_pred[-recent_period:]
                # 这里需要对应的真实值，简化处理使用整体测试MSE
                recent_mse = model_info['test_mse']
            else:
                recent_mse = model_info['test_mse']
            
            recent_mses.append(recent_mse)
        
        # 转换为权重
        recent_mses = np.array(recent_mses)
        inverse_mses = 1.0 / (recent_mses + 1e-8)
        weights = inverse_mses / np.sum(inverse_mses)
        
        return weights
    
    def create_stacking_ensemble(self):
        """创建堆叠集成模型"""
        print("\n=== 创建堆叠集成模型 ===")
        
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        stacking_models = {}
        
        for target_col in target_cols:
            print(f"\n为 {target_col} 创建堆叠集成...")
            
            models = self.trained_models[target_col]
            
            # 收集基础模型的预测作为元特征
            train_meta_features = []
            test_meta_features = []

            # 找到最短的预测长度
            min_train_len = float('inf')
            min_test_len = float('inf')

            for model_name, model_info in models.items():
                train_pred = model_info['train_pred']
                test_pred = model_info['test_pred']
                min_train_len = min(min_train_len, len(train_pred))
                min_test_len = min(min_test_len, len(test_pred))

            # 调整所有预测到相同长度
            for model_name, model_info in models.items():
                train_pred = model_info['train_pred']
                test_pred = model_info['test_pred']

                # 取最后的min_len个预测值
                train_meta_features.append(train_pred[-min_train_len:])
                test_meta_features.append(test_pred[-min_test_len:])

            # 转换为数组
            X_meta_train = np.column_stack(train_meta_features)
            X_meta_test = np.column_stack(test_meta_features)
            
            # 准备目标值
            df = self.enhanced_df.dropna().reset_index(drop=True)
            train_df = df[df['期号'] <= 150].copy()
            test_df = df[df['期号'] >= 151].copy()

            y_train = train_df[target_col]
            y_test = test_df[target_col]

            # 调整目标值长度以匹配预测长度
            y_train = y_train.iloc[-min_train_len:].reset_index(drop=True)
            y_test = y_test.iloc[-min_test_len:].reset_index(drop=True)
            
            # 训练元学习器
            meta_learner = Ridge(alpha=1.0, random_state=42)
            meta_learner.fit(X_meta_train, y_train)
            
            # 预测
            stacking_train_pred = meta_learner.predict(X_meta_train)
            stacking_test_pred = meta_learner.predict(X_meta_test)
            
            # 计算性能
            train_mse = mean_squared_error(y_train, stacking_train_pred)
            test_mse = mean_squared_error(y_test, stacking_test_pred)
            
            stacking_models[target_col] = {
                'meta_learner': meta_learner,
                'train_mse': train_mse,
                'test_mse': test_mse,
                'train_pred': stacking_train_pred,
                'test_pred': stacking_test_pred
            }
            
            print(f"  堆叠集成性能 - 训练MSE: {train_mse:.4f}, 测试MSE: {test_mse:.4f}")
        
        self.stacking_models = stacking_models
        return stacking_models

    def create_optimized_voting_ensemble(self):
        """创建优化的投票集成"""
        print("\n=== 创建优化投票集成 ===")

        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        voting_models = {}

        for target_col in target_cols:
            print(f"\n为 {target_col} 创建优化投票集成...")

            models = self.trained_models[target_col]
            weights = list(self.dynamic_weights[target_col].values())

            # 使用动态权重进行加权平均
            train_predictions = []
            test_predictions = []

            for model_name, model_info in models.items():
                train_predictions.append(model_info['train_pred'])
                test_predictions.append(model_info['test_pred'])

            # 加权平均预测
            weighted_train_pred = np.average(train_predictions, axis=0, weights=weights)
            weighted_test_pred = np.average(test_predictions, axis=0, weights=weights)

            # 计算性能
            df = self.enhanced_df.dropna().reset_index(drop=True)
            train_df = df[df['期号'] <= 150].copy()
            test_df = df[df['期号'] >= 151].copy()

            y_train = train_df[target_col]
            y_test = test_df[target_col]

            # 调整长度
            if len(weighted_train_pred) != len(y_train):
                min_len = min(len(weighted_train_pred), len(y_train))
                weighted_train_pred = weighted_train_pred[-min_len:]
                y_train = y_train.iloc[-min_len:]

            if len(weighted_test_pred) != len(y_test):
                min_len = min(len(weighted_test_pred), len(y_test))
                weighted_test_pred = weighted_test_pred[-min_len:]
                y_test = y_test.iloc[-min_len:]

            train_mse = mean_squared_error(y_train, weighted_train_pred)
            test_mse = mean_squared_error(y_test, weighted_test_pred)

            voting_models[target_col] = {
                'weights': weights,
                'train_mse': train_mse,
                'test_mse': test_mse,
                'train_pred': weighted_train_pred,
                'test_pred': weighted_test_pred
            }

            print(f"  加权投票性能 - 训练MSE: {train_mse:.4f}, 测试MSE: {test_mse:.4f}")

        self.voting_models = voting_models
        return voting_models

    def evaluate_ensemble_performance(self):
        """评估集成模型性能"""
        print("\n=== 评估集成模型性能 ===")

        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']

        # 准备真实值
        df = self.enhanced_df.dropna().reset_index(drop=True)
        test_df = df[df['期号'] >= 151].copy()

        # 收集所有预测结果
        all_predictions = []
        all_actuals = []

        for target_col in target_cols:
            # 使用堆叠集成的预测（通常性能最好）
            if hasattr(self, 'stacking_models'):
                predictions = self.stacking_models[target_col]['test_pred']
            else:
                predictions = self.voting_models[target_col]['test_pred']

            actuals = test_df[target_col].values

            # 调整长度
            min_len = min(len(predictions), len(actuals))
            predictions = predictions[-min_len:]
            actuals = actuals[-min_len:]

            all_predictions.append(predictions)
            all_actuals.append(actuals)

        # 转换为数组
        all_predictions = np.array(all_predictions).T  # shape: (n_periods, 6)
        all_actuals = np.array(all_actuals).T

        # 计算每期正确预测的数字个数
        correct_per_period = []
        for pred, actual in zip(all_predictions, all_actuals):
            # 四舍五入到最近的整数
            pred_rounded = np.round(pred).astype(int)
            # 确保在有效范围内
            pred_rounded = np.clip(pred_rounded, 1, 49)

            # 计算正确预测的个数
            pred_set = set(pred_rounded)
            actual_set = set(actual)
            correct_count = len(pred_set.intersection(actual_set))
            correct_per_period.append(correct_count)

        # 计算性能指标
        avg_correct = np.mean(correct_per_period)
        max_correct = np.max(correct_per_period)
        std_correct = np.std(correct_per_period)

        performance_results = {
            'avg_correct_per_period': avg_correct,
            'max_correct_per_period': max_correct,
            'std_correct_per_period': std_correct,
            'correct_per_period': correct_per_period,
            'total_periods': len(correct_per_period)
        }

        print(f"集成模型性能评估：")
        print(f"  平均正确预测数字: {avg_correct:.3f} 个/期")
        print(f"  最佳单期表现: {max_correct} 个数字")
        print(f"  标准差: {std_correct:.3f}")
        print(f"  测试期数: {len(correct_per_period)}")

        return performance_results

    def create_performance_visualization(self):
        """创建性能可视化"""
        print("\n=== 创建性能可视化 ===")

        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']

        # 1. 模型性能对比
        model_names = []
        test_mses = []

        for target_col in target_cols:
            for model_name, model_info in self.trained_models[target_col].items():
                model_names.append(f"{target_col}_{model_name}")
                test_mses.append(model_info['test_mse'])

        # 选择前20个进行可视化
        sorted_indices = np.argsort(test_mses)[:20]
        top_models = [model_names[i] for i in sorted_indices]
        top_mses = [test_mses[i] for i in sorted_indices]

        axes[0, 0].barh(range(len(top_models)), top_mses, alpha=0.7)
        axes[0, 0].set_yticks(range(len(top_models)))
        axes[0, 0].set_yticklabels([name.replace('_', '\n') for name in top_models], fontsize=8)
        axes[0, 0].set_xlabel('测试MSE')
        axes[0, 0].set_title('模型性能对比（前20名）')
        axes[0, 0].grid(True, alpha=0.3)

        # 2. 动态权重分布
        if hasattr(self, 'dynamic_weights'):
            sample_target = '数字1'
            weights = list(self.dynamic_weights[sample_target].values())
            labels = list(self.dynamic_weights[sample_target].keys())

            axes[0, 1].pie(weights, labels=labels, autopct='%1.1f%%', startangle=90)
            axes[0, 1].set_title(f'{sample_target} 动态权重分布')

        # 3. 集成方法性能对比
        if hasattr(self, 'stacking_models') and hasattr(self, 'voting_models'):
            ensemble_mses = []

            for target_col in target_cols:
                stacking_mse = self.stacking_models[target_col]['test_mse']
                voting_mse = self.voting_models[target_col]['test_mse']
                ensemble_mses.append([stacking_mse, voting_mse])

            ensemble_mses = np.array(ensemble_mses)
            x = np.arange(len(target_cols))
            width = 0.35

            axes[1, 0].bar(x - width/2, ensemble_mses[:, 0], width, label='Stacking', alpha=0.7)
            axes[1, 0].bar(x + width/2, ensemble_mses[:, 1], width, label='Weighted Voting', alpha=0.7)
            axes[1, 0].set_xlabel('目标数字')
            axes[1, 0].set_ylabel('测试MSE')
            axes[1, 0].set_title('集成方法性能对比')
            axes[1, 0].set_xticks(x)
            axes[1, 0].set_xticklabels(target_cols)
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)

        # 4. 预测准确率趋势
        if hasattr(self, 'performance_results'):
            correct_per_period = self.performance_results['correct_per_period']
            periods = range(151, 151 + len(correct_per_period))

            axes[1, 1].plot(periods, correct_per_period, marker='o', linewidth=2, markersize=6)
            axes[1, 1].axhline(y=np.mean(correct_per_period), color='r', linestyle='--',
                              label=f'平均: {np.mean(correct_per_period):.2f}')
            axes[1, 1].set_xlabel('期号')
            axes[1, 1].set_ylabel('正确预测数字个数')
            axes[1, 1].set_title('预测准确率趋势')
            axes[1, 1].legend()
            axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('advanced_ensemble_performance.png', dpi=300, bbox_inches='tight')
        plt.show()

    def run_advanced_ensemble_optimization(self):
        """运行完整的高级集成优化流程"""
        print("开始高级模型集成优化")
        print("=" * 60)

        # 1. 加载增强数据
        self.load_enhanced_data()

        # 2. 创建高级基础模型
        self.create_advanced_base_models()

        # 3. 训练基础模型
        self.train_base_models()

        # 4. 实施动态权重调整
        self.implement_dynamic_weighting()

        # 5. 创建堆叠集成
        self.create_stacking_ensemble()

        # 6. 创建优化投票集成
        self.create_optimized_voting_ensemble()

        # 7. 评估集成性能
        self.performance_results = self.evaluate_ensemble_performance()

        # 8. 创建可视化
        self.create_performance_visualization()

        print("\n" + "=" * 60)
        print("✅ 高级模型集成优化完成！")
        print(f"🎯 平均正确预测: {self.performance_results['avg_correct_per_period']:.3f} 个数字/期")
        print(f"🏆 最佳单期表现: {self.performance_results['max_correct_per_period']} 个数字")
        print(f"📊 基础模型数量: {len(self.base_models)}")
        print(f"🔄 集成方法: 堆叠集成 + 动态权重投票")

        return {
            'trained_models': self.trained_models,
            'stacking_models': self.stacking_models,
            'voting_models': self.voting_models,
            'dynamic_weights': self.dynamic_weights,
            'performance_results': self.performance_results
        }

if __name__ == "__main__":
    optimizer = AdvancedEnsembleOptimization()
    results = optimizer.run_advanced_ensemble_optimization()
