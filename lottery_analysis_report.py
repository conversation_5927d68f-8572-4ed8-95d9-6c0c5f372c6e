#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
彩票数据分析报告和改进建议
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class LotteryAnalysisReport:
    def __init__(self):
        self.df = pd.read_csv('lottery_data.csv')
        
    def generate_comprehensive_report(self):
        """生成综合分析报告"""
        print("=" * 60)
        print("彩票数据分析综合报告")
        print("=" * 60)
        
        # 1. 数据基本统计
        self._basic_statistics()
        
        # 2. 深度模式分析
        self._pattern_analysis()
        
        # 3. 预测难度分析
        self._prediction_difficulty_analysis()
        
        # 4. 模型性能分析
        self._model_performance_analysis()
        
        # 5. 改进建议
        self._improvement_suggestions()
        
    def _basic_statistics(self):
        """基本统计分析"""
        print("\n1. 数据基本统计")
        print("-" * 30)
        
        # 数字出现频率分析
        all_numbers = []
        for col in ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6', '特码']:
            all_numbers.extend(self.df[col].tolist())
        
        number_counts = Counter(all_numbers)
        
        print(f"总期数：{len(self.df)}")
        print(f"数字范围：{min(all_numbers)} - {max(all_numbers)}")
        print(f"最热门数字：{number_counts.most_common(1)[0]} (出现{number_counts.most_common(1)[0][1]}次)")
        print(f"最冷门数字：{number_counts.most_common()[-1]} (出现{number_counts.most_common()[-1][1]}次)")
        
        # 计算变异系数
        frequencies = list(number_counts.values())
        cv = np.std(frequencies) / np.mean(frequencies)
        print(f"频率变异系数：{cv:.3f} (越小说明分布越均匀)")
        
    def _pattern_analysis(self):
        """深度模式分析"""
        print("\n2. 深度模式分析")
        print("-" * 30)
        
        # 连续性分析
        consecutive_patterns = []
        for _, row in self.df.iterrows():
            numbers = sorted([row['数字1'], row['数字2'], row['数字3'], 
                            row['数字4'], row['数字5'], row['数字6']])
            consecutive_count = 0
            for i in range(len(numbers)-1):
                if numbers[i+1] - numbers[i] == 1:
                    consecutive_count += 1
            consecutive_patterns.append(consecutive_count)
        
        print(f"平均连号个数：{np.mean(consecutive_patterns):.2f}")
        print(f"连号个数分布：{Counter(consecutive_patterns)}")
        
        # 重复号分析
        repeat_patterns = []
        for i in range(1, len(self.df)):
            current = set([self.df.iloc[i]['数字1'], self.df.iloc[i]['数字2'], 
                          self.df.iloc[i]['数字3'], self.df.iloc[i]['数字4'], 
                          self.df.iloc[i]['数字5'], self.df.iloc[i]['数字6']])
            previous = set([self.df.iloc[i-1]['数字1'], self.df.iloc[i-1]['数字2'], 
                           self.df.iloc[i-1]['数字3'], self.df.iloc[i-1]['数字4'], 
                           self.df.iloc[i-1]['数字5'], self.df.iloc[i-1]['数字6']])
            repeat_count = len(current.intersection(previous))
            repeat_patterns.append(repeat_count)
        
        print(f"平均重复号个数：{np.mean(repeat_patterns):.2f}")
        print(f"重复号个数分布：{Counter(repeat_patterns)}")
        
        # 和值分析
        sum_values = []
        for _, row in self.df.iterrows():
            sum_val = sum([row['数字1'], row['数字2'], row['数字3'], 
                          row['数字4'], row['数字5'], row['数字6']])
            sum_values.append(sum_val)
        
        print(f"和值统计：均值={np.mean(sum_values):.1f}, 标准差={np.std(sum_values):.1f}")
        print(f"和值范围：{min(sum_values)} - {max(sum_values)}")
        
    def _prediction_difficulty_analysis(self):
        """预测难度分析"""
        print("\n3. 预测难度分析")
        print("-" * 30)
        
        # 计算理论随机命中率
        total_combinations = 1
        for i in range(6):
            total_combinations *= (49 - i) / (i + 1)
        
        single_number_prob = 6 / 49  # 单个数字被选中的概率
        print(f"理论上随机选择命中单个数字的概率：{single_number_prob:.3f}")
        print(f"理论上随机选择平均命中数字个数：{6 * single_number_prob:.2f}")
        
        # 分析数字分布的随机性
        # 使用卡方检验分析数字分布是否符合均匀分布
        all_numbers = []
        for col in ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']:
            all_numbers.extend(self.df[col].tolist())
        
        number_counts = Counter(all_numbers)
        expected_freq = len(all_numbers) / 49  # 期望频率
        
        chi_square = sum((count - expected_freq) ** 2 / expected_freq 
                        for count in number_counts.values())
        
        print(f"卡方统计量：{chi_square:.2f}")
        print("(卡方值越大，说明分布越不均匀，越难预测)")
        
    def _model_performance_analysis(self):
        """模型性能分析"""
        print("\n4. 模型性能分析")
        print("-" * 30)
        
        print("随机森林模型结果分析：")
        print("- 平均每期预测对 0.20 个数字")
        print("- 随机基准：平均每期预测对 0.40 个数字")
        print("- 模型表现不如随机基准")
        
        print("\n可能的原因：")
        print("1. 彩票数字本身具有很强的随机性")
        print("2. 历史数据中可能不存在可学习的模式")
        print("3. 特征工程可能不够有效")
        print("4. 模型复杂度可能不适合这个问题")
        print("5. 训练数据量相对较少（121期）")
        
    def _improvement_suggestions(self):
        """改进建议"""
        print("\n5. 改进建议")
        print("-" * 30)
        
        print("A. 特征工程改进：")
        print("   1. 尝试更复杂的组合特征")
        print("   2. 考虑季节性、周期性特征")
        print("   3. 引入外部因素（如日期、节假日等）")
        print("   4. 使用更长的历史窗口")
        
        print("\nB. 模型改进：")
        print("   1. 尝试集成学习方法")
        print("   2. 使用深度学习模型（如Transformer）")
        print("   3. 考虑概率预测而非点预测")
        print("   4. 使用贝叶斯方法处理不确定性")
        
        print("\nC. 评估方法改进：")
        print("   1. 使用更多样的评估指标")
        print("   2. 考虑部分匹配的奖励机制")
        print("   3. 分析预测的置信度")
        print("   4. 进行更长期的回测")
        
        print("\nD. 现实建议：")
        print("   1. 彩票具有很强的随机性，AI预测效果有限")
        print("   2. 任何预测模型都不能保证盈利")
        print("   3. 理性对待彩票，不要过度依赖预测")
        print("   4. 可以将此作为数据科学学习项目")
        
    def create_visualization_summary(self):
        """创建可视化总结"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # 1. 数字频率分布
        all_numbers = []
        for col in ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']:
            all_numbers.extend(self.df[col].tolist())
        
        number_counts = Counter(all_numbers)
        numbers = list(range(1, 50))
        frequencies = [number_counts.get(num, 0) for num in numbers]
        
        axes[0, 0].bar(numbers, frequencies, alpha=0.7)
        axes[0, 0].set_title('数字出现频率分布')
        axes[0, 0].set_xlabel('数字')
        axes[0, 0].set_ylabel('出现次数')
        
        # 2. 和值分布
        sum_values = []
        for _, row in self.df.iterrows():
            sum_val = sum([row['数字1'], row['数字2'], row['数字3'], 
                          row['数字4'], row['数字5'], row['数字6']])
            sum_values.append(sum_val)
        
        axes[0, 1].hist(sum_values, bins=20, alpha=0.7, edgecolor='black')
        axes[0, 1].set_title('和值分布')
        axes[0, 1].set_xlabel('和值')
        axes[0, 1].set_ylabel('频次')
        
        # 3. 连号个数分布
        consecutive_patterns = []
        for _, row in self.df.iterrows():
            numbers = sorted([row['数字1'], row['数字2'], row['数字3'], 
                            row['数字4'], row['数字5'], row['数字6']])
            consecutive_count = 0
            for i in range(len(numbers)-1):
                if numbers[i+1] - numbers[i] == 1:
                    consecutive_count += 1
            consecutive_patterns.append(consecutive_count)
        
        consecutive_counter = Counter(consecutive_patterns)
        axes[0, 2].bar(consecutive_counter.keys(), consecutive_counter.values(), alpha=0.7)
        axes[0, 2].set_title('连号个数分布')
        axes[0, 2].set_xlabel('连号个数')
        axes[0, 2].set_ylabel('频次')
        
        # 4. 奇偶分布
        odd_counts = []
        for _, row in self.df.iterrows():
            numbers = [row['数字1'], row['数字2'], row['数字3'], 
                      row['数字4'], row['数字5'], row['数字6']]
            odd_count = sum(1 for num in numbers if num % 2 == 1)
            odd_counts.append(odd_count)
        
        odd_counter = Counter(odd_counts)
        axes[1, 0].bar(odd_counter.keys(), odd_counter.values(), alpha=0.7)
        axes[1, 0].set_title('每期奇数个数分布')
        axes[1, 0].set_xlabel('奇数个数')
        axes[1, 0].set_ylabel('频次')
        
        # 5. 大小数分布
        big_counts = []
        for _, row in self.df.iterrows():
            numbers = [row['数字1'], row['数字2'], row['数字3'], 
                      row['数字4'], row['数字5'], row['数字6']]
            big_count = sum(1 for num in numbers if num > 25)
            big_counts.append(big_count)
        
        big_counter = Counter(big_counts)
        axes[1, 1].bar(big_counter.keys(), big_counter.values(), alpha=0.7)
        axes[1, 1].set_title('每期大数个数分布(>25)')
        axes[1, 1].set_xlabel('大数个数')
        axes[1, 1].set_ylabel('频次')
        
        # 6. 重复号分布
        repeat_patterns = []
        for i in range(1, len(self.df)):
            current = set([self.df.iloc[i]['数字1'], self.df.iloc[i]['数字2'], 
                          self.df.iloc[i]['数字3'], self.df.iloc[i]['数字4'], 
                          self.df.iloc[i]['数字5'], self.df.iloc[i]['数字6']])
            previous = set([self.df.iloc[i-1]['数字1'], self.df.iloc[i-1]['数字2'], 
                           self.df.iloc[i-1]['数字3'], self.df.iloc[i-1]['数字4'], 
                           self.df.iloc[i-1]['数字5'], self.df.iloc[i-1]['数字6']])
            repeat_count = len(current.intersection(previous))
            repeat_patterns.append(repeat_count)
        
        repeat_counter = Counter(repeat_patterns)
        axes[1, 2].bar(repeat_counter.keys(), repeat_counter.values(), alpha=0.7)
        axes[1, 2].set_title('重复号个数分布')
        axes[1, 2].set_xlabel('重复号个数')
        axes[1, 2].set_ylabel('频次')
        
        plt.tight_layout()
        plt.savefig('lottery_comprehensive_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("综合分析图表已保存为 lottery_comprehensive_analysis.png")

if __name__ == "__main__":
    # 创建分析报告
    report = LotteryAnalysisReport()
    
    # 生成综合报告
    report.generate_comprehensive_report()
    
    # 创建可视化总结
    report.create_visualization_summary()
