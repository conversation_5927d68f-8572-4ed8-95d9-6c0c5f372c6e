#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能对比分析
比较新数据集性能与之前141-160期的性能，分析变化原因和模型稳定性
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class PerformanceComparisonAnalysis:
    def __init__(self):
        self.previous_performance = None
        self.current_performance = None
        self.comparison_results = {}
        
    def load_performance_data(self):
        """加载性能数据"""
        print("=== 加载性能数据 ===")
        
        # 之前的性能数据（141-160期测试集）
        self.previous_performance = {
            'test_period': '141-160期',
            'avg_correct': 0.950,  # 之前的最终优化结果
            'max_correct': 3,
            'test_periods': 20,
            'training_periods': 140,
            'description': '基于1-140期训练，141-160期测试'
        }
        
        # 当前的性能数据（151-170期测试集）
        self.current_performance = {
            'test_period': '151-170期',
            'avg_correct': 0.900,  # 刚才的训练结果
            'max_correct': 3,
            'test_periods': 20,
            'training_periods': 150,
            'description': '基于1-150期训练，151-170期测试'
        }
        
        print(f"之前性能: {self.previous_performance['avg_correct']:.3f} 个数字/期 ({self.previous_performance['test_period']})")
        print(f"当前性能: {self.current_performance['avg_correct']:.3f} 个数字/期 ({self.current_performance['test_period']})")
        
    def analyze_performance_change(self):
        """分析性能变化"""
        print("\n=== 性能变化分析 ===")
        
        prev_perf = self.previous_performance['avg_correct']
        curr_perf = self.current_performance['avg_correct']
        
        # 计算变化
        absolute_change = curr_perf - prev_perf
        relative_change = (curr_perf - prev_perf) / prev_perf * 100
        
        print(f"性能变化分析：")
        print(f"  之前性能: {prev_perf:.3f} 个数字/期")
        print(f"  当前性能: {curr_perf:.3f} 个数字/期")
        print(f"  绝对变化: {absolute_change:+.3f} 个数字/期")
        print(f"  相对变化: {relative_change:+.1f}%")
        
        if absolute_change > 0:
            print("  📈 性能有所提升")
        elif absolute_change < 0:
            print("  📉 性能有所下降")
        else:
            print("  ➡️ 性能保持稳定")
        
        self.comparison_results['performance_change'] = {
            'absolute_change': absolute_change,
            'relative_change': relative_change,
            'trend': 'improvement' if absolute_change > 0 else 'decline' if absolute_change < 0 else 'stable'
        }
        
    def analyze_training_data_impact(self):
        """分析训练数据量增加的影响"""
        print("\n=== 训练数据量影响分析 ===")
        
        prev_train = self.previous_performance['training_periods']
        curr_train = self.current_performance['training_periods']
        
        data_increase = curr_train - prev_train
        data_increase_pct = (curr_train - prev_train) / prev_train * 100
        
        print(f"训练数据量变化：")
        print(f"  之前训练数据: {prev_train}期")
        print(f"  当前训练数据: {curr_train}期")
        print(f"  数据量增加: {data_increase}期 (+{data_increase_pct:.1f}%)")
        
        # 分析数据量与性能的关系
        performance_per_data = self.current_performance['avg_correct'] / curr_train * 1000
        prev_performance_per_data = self.previous_performance['avg_correct'] / prev_train * 1000
        
        print(f"  数据效率分析:")
        print(f"    之前: {prev_performance_per_data:.2f} 性能点/千期数据")
        print(f"    当前: {performance_per_data:.2f} 性能点/千期数据")
        
        self.comparison_results['training_data_impact'] = {
            'data_increase': data_increase,
            'data_increase_pct': data_increase_pct,
            'efficiency_change': performance_per_data - prev_performance_per_data
        }
        
    def analyze_temporal_stability(self):
        """分析时间稳定性"""
        print("\n=== 时间稳定性分析 ===")
        
        # 读取实际数据进行更详细的分析
        try:
            df = pd.read_csv('updated_lottery_data.csv')
            
            # 分析不同时间段的数据特征
            period_141_160 = df[(df['期号'] >= 141) & (df['期号'] <= 160)]
            period_151_170 = df[(df['期号'] >= 151) & (df['期号'] <= 170)]
            
            print("数据特征对比：")
            
            # 和值分析
            sum_141_160 = period_141_160[['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']].sum(axis=1)
            sum_151_170 = period_151_170[['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']].sum(axis=1)
            
            print(f"  和值统计:")
            print(f"    141-160期: 均值={sum_141_160.mean():.1f}, 标准差={sum_141_160.std():.1f}")
            print(f"    151-170期: 均值={sum_151_170.mean():.1f}, 标准差={sum_151_170.std():.1f}")
            
            # 奇偶分析
            def count_odd_numbers(row):
                return sum(1 for x in [row['数字1'], row['数字2'], row['数字3'], 
                                     row['数字4'], row['数字5'], row['数字6']] if x % 2 == 1)
            
            odd_141_160 = period_141_160.apply(count_odd_numbers, axis=1)
            odd_151_170 = period_151_170.apply(count_odd_numbers, axis=1)
            
            print(f"  奇数个数:")
            print(f"    141-160期: 均值={odd_141_160.mean():.1f}, 标准差={odd_141_160.std():.1f}")
            print(f"    151-170期: 均值={odd_151_170.mean():.1f}, 标准差={odd_151_170.std():.1f}")
            
            # 大小数分析
            def count_large_numbers(row):
                return sum(1 for x in [row['数字1'], row['数字2'], row['数字3'], 
                                     row['数字4'], row['数字5', '数字6']] if x > 25)
            
            large_141_160 = period_141_160.apply(count_large_numbers, axis=1)
            large_151_170 = period_151_170.apply(count_large_numbers, axis=1)
            
            print(f"  大数个数:")
            print(f"    141-160期: 均值={large_141_160.mean():.1f}, 标准差={large_141_160.std():.1f}")
            print(f"    151-170期: 均值={large_151_170.mean():.1f}, 标准差={large_151_170.std():.1f}")
            
            # 统计显著性检验
            sum_ttest = stats.ttest_ind(sum_141_160, sum_151_170)
            odd_ttest = stats.ttest_ind(odd_141_160, odd_151_170)
            
            print(f"\n  统计显著性检验:")
            print(f"    和值差异: t={sum_ttest.statistic:.3f}, p={sum_ttest.pvalue:.3f}")
            print(f"    奇数个数差异: t={odd_ttest.statistic:.3f}, p={odd_ttest.pvalue:.3f}")
            
            if sum_ttest.pvalue < 0.05:
                print("    ⚠️ 和值分布存在显著差异")
            else:
                print("    ✅ 和值分布无显著差异")
                
            self.comparison_results['temporal_stability'] = {
                'sum_stats': {
                    'period_141_160': {'mean': sum_141_160.mean(), 'std': sum_141_160.std()},
                    'period_151_170': {'mean': sum_151_170.mean(), 'std': sum_151_170.std()},
                    'ttest_p': sum_ttest.pvalue
                },
                'odd_stats': {
                    'period_141_160': {'mean': odd_141_160.mean(), 'std': odd_141_160.std()},
                    'period_151_170': {'mean': odd_151_170.mean(), 'std': odd_151_170.std()},
                    'ttest_p': odd_ttest.pvalue
                }
            }
            
        except Exception as e:
            print(f"数据分析出错: {e}")
    
    def analyze_model_stability(self):
        """分析模型稳定性"""
        print("\n=== 模型稳定性分析 ===")
        
        # 基于性能变化分析模型稳定性
        perf_change = abs(self.comparison_results['performance_change']['absolute_change'])
        
        print(f"模型稳定性评估：")
        print(f"  性能变化幅度: {perf_change:.3f} 个数字/期")
        
        if perf_change <= 0.05:
            stability_level = "非常稳定"
            stability_score = 5
        elif perf_change <= 0.10:
            stability_level = "稳定"
            stability_score = 4
        elif perf_change <= 0.20:
            stability_level = "较稳定"
            stability_score = 3
        elif perf_change <= 0.30:
            stability_level = "不太稳定"
            stability_score = 2
        else:
            stability_level = "不稳定"
            stability_score = 1
        
        print(f"  稳定性等级: {stability_level} (评分: {stability_score}/5)")
        
        # 分析可能的影响因素
        print(f"\n  影响因素分析:")
        
        data_increase = self.comparison_results['training_data_impact']['data_increase']
        if data_increase > 0:
            print(f"    ✅ 训练数据增加{data_increase}期，有助于提升模型泛化能力")
        
        if perf_change > 0.1:
            print(f"    ⚠️ 性能变化较大，可能原因：")
            print(f"      - 新测试期数据分布与训练数据存在差异")
            print(f"      - 模型对新数据的适应性有限")
            print(f"      - 彩票数据的内在随机性影响")
        
        self.comparison_results['model_stability'] = {
            'stability_level': stability_level,
            'stability_score': stability_score,
            'performance_change_magnitude': perf_change
        }
    
    def create_comparison_visualization(self):
        """创建对比可视化"""
        print("\n=== 创建对比可视化 ===")
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 1. 性能对比
        periods = ['141-160期\n(之前)', '151-170期\n(当前)']
        performances = [self.previous_performance['avg_correct'], 
                       self.current_performance['avg_correct']]
        colors = ['lightblue', 'lightgreen']
        
        bars = axes[0, 0].bar(periods, performances, color=colors, alpha=0.7)
        axes[0, 0].set_ylabel('平均预测正确数字个数')
        axes[0, 0].set_title('性能对比')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 标注数值
        for bar, perf in zip(bars, performances):
            axes[0, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                           f'{perf:.3f}', ha='center', va='bottom', fontweight='bold')
        
        # 2. 训练数据量对比
        train_periods = [self.previous_performance['training_periods'],
                        self.current_performance['training_periods']]
        
        axes[0, 1].bar(periods, train_periods, color=['orange', 'red'], alpha=0.7)
        axes[0, 1].set_ylabel('训练数据量（期）')
        axes[0, 1].set_title('训练数据量对比')
        axes[0, 1].grid(True, alpha=0.3)
        
        for i, (period, train) in enumerate(zip(periods, train_periods)):
            axes[0, 1].text(i, train + 2, f'{train}期', ha='center', va='bottom', fontweight='bold')
        
        # 3. 性能变化趋势
        x = [1, 2]
        y = performances
        axes[1, 0].plot(x, y, marker='o', linewidth=3, markersize=10, color='blue')
        axes[1, 0].set_xticks(x)
        axes[1, 0].set_xticklabels(['之前\n(141-160期)', '当前\n(151-170期)'])
        axes[1, 0].set_ylabel('平均预测正确数字个数')
        axes[1, 0].set_title('性能变化趋势')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 添加变化箭头和数值
        change = self.comparison_results['performance_change']['absolute_change']
        if change > 0:
            axes[1, 0].annotate(f'+{change:.3f}', xy=(1.5, (y[0] + y[1])/2), 
                               xytext=(1.5, (y[0] + y[1])/2 + 0.1),
                               arrowprops=dict(arrowstyle='->', color='green'),
                               ha='center', color='green', fontweight='bold')
        elif change < 0:
            axes[1, 0].annotate(f'{change:.3f}', xy=(1.5, (y[0] + y[1])/2), 
                               xytext=(1.5, (y[0] + y[1])/2 - 0.1),
                               arrowprops=dict(arrowstyle='->', color='red'),
                               ha='center', color='red', fontweight='bold')
        
        # 4. 稳定性评估雷达图
        stability_score = self.comparison_results['model_stability']['stability_score']
        categories = ['性能稳定性', '数据适应性', '泛化能力', '预测一致性']
        scores = [stability_score, 4, 3, 3]  # 示例评分
        
        angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
        scores += scores[:1]  # 闭合图形
        angles += angles[:1]
        
        axes[1, 1].plot(angles, scores, 'o-', linewidth=2, color='purple')
        axes[1, 1].fill(angles, scores, alpha=0.25, color='purple')
        axes[1, 1].set_xticks(angles[:-1])
        axes[1, 1].set_xticklabels(categories)
        axes[1, 1].set_ylim(0, 5)
        axes[1, 1].set_title('模型稳定性评估')
        axes[1, 1].grid(True)
        
        plt.tight_layout()
        plt.savefig('performance_comparison_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def generate_comparison_report(self):
        """生成对比报告"""
        print("\n=== 生成对比报告 ===")
        
        report = f"""
# 性能对比分析报告

## 概述
本报告对比分析了彩票预测模型在不同测试期间的性能表现，评估模型的稳定性和泛化能力。

## 性能对比结果

### 基本性能指标
- **之前测试期间**: {self.previous_performance['test_period']}
  - 平均预测正确数字: {self.previous_performance['avg_correct']:.3f} 个/期
  - 最佳单期表现: {self.previous_performance['max_correct']} 个数字
  - 训练数据量: {self.previous_performance['training_periods']} 期

- **当前测试期间**: {self.current_performance['test_period']}
  - 平均预测正确数字: {self.current_performance['avg_correct']:.3f} 个/期
  - 最佳单期表现: {self.current_performance['max_correct']} 个数字
  - 训练数据量: {self.current_performance['training_periods']} 期

### 性能变化分析
- **绝对变化**: {self.comparison_results['performance_change']['absolute_change']:+.3f} 个数字/期
- **相对变化**: {self.comparison_results['performance_change']['relative_change']:+.1f}%
- **变化趋势**: {self.comparison_results['performance_change']['trend']}

### 训练数据影响
- **数据量增加**: {self.comparison_results['training_data_impact']['data_increase']} 期 ({self.comparison_results['training_data_impact']['data_increase_pct']:+.1f}%)
- **数据效率变化**: {self.comparison_results['training_data_impact']['efficiency_change']:+.2f} 性能点/千期数据

### 模型稳定性评估
- **稳定性等级**: {self.comparison_results['model_stability']['stability_level']}
- **稳定性评分**: {self.comparison_results['model_stability']['stability_score']}/5
- **性能变化幅度**: {self.comparison_results['model_stability']['performance_change_magnitude']:.3f} 个数字/期

## 分析结论

### 主要发现
1. **性能表现**: 当前模型在新测试期间的表现为{self.current_performance['avg_correct']:.3f}个数字/期，相比之前{'略有下降' if self.comparison_results['performance_change']['absolute_change'] < 0 else '有所提升' if self.comparison_results['performance_change']['absolute_change'] > 0 else '保持稳定'}

2. **数据量效应**: 训练数据增加{self.comparison_results['training_data_impact']['data_increase']}期，为模型提供了更多学习样本

3. **稳定性评价**: 模型表现出{self.comparison_results['model_stability']['stability_level']}的稳定性

### 影响因素分析
1. **数据分布变化**: 不同时间段的彩票数据可能存在微妙的分布差异
2. **模型适应性**: 模型对新数据的适应能力体现了其泛化性能
3. **随机性影响**: 彩票数据的内在随机性是影响预测稳定性的根本因素

### 改进建议
1. **持续学习**: 考虑实施在线学习机制，让模型能够适应数据分布的变化
2. **集成策略**: 进一步优化模型集成策略，提高预测稳定性
3. **特征工程**: 探索更多能够捕获时间变化模式的特征
4. **正则化**: 加强模型正则化，防止过拟合新的训练数据

---
*报告生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}*
        """
        
        with open('performance_comparison_report.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        print("对比报告已生成: performance_comparison_report.md")
    
    def run_performance_comparison(self):
        """运行完整的性能对比分析"""
        print("开始性能对比分析")
        print("=" * 60)
        
        # 1. 加载性能数据
        self.load_performance_data()
        
        # 2. 分析性能变化
        self.analyze_performance_change()
        
        # 3. 分析训练数据影响
        self.analyze_training_data_impact()
        
        # 4. 分析时间稳定性
        self.analyze_temporal_stability()
        
        # 5. 分析模型稳定性
        self.analyze_model_stability()
        
        # 6. 创建可视化
        self.create_comparison_visualization()
        
        # 7. 生成报告
        self.generate_comparison_report()
        
        print("\n" + "=" * 60)
        print("✅ 性能对比分析完成！")
        print(f"📊 性能变化: {self.comparison_results['performance_change']['absolute_change']:+.3f} 个数字/期")
        print(f"🎯 稳定性等级: {self.comparison_results['model_stability']['stability_level']}")
        print(f"📋 详细报告: performance_comparison_report.md")
        
        return self.comparison_results

if __name__ == "__main__":
    analyzer = PerformanceComparisonAnalysis()
    results = analyzer.run_performance_comparison()
