#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型重新训练
使用最优模型组合在新训练集（1-150期）上重新训练，保持相同的特征工程和模型选择策略
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, VotingRegressor
from sklearn.neural_network import MLPRegressor
from sklearn.linear_model import Ridge
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import mean_squared_error, mean_absolute_error
from sklearn.preprocessing import StandardScaler
from sklearn.feature_selection import mutual_info_regression
from sklearn.decomposition import PCA
import xgboost as xgb
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class UpdatedModelTraining:
    def __init__(self):
        self.train_df = None
        self.test_df = None
        self.enhanced_train_df = None
        self.enhanced_test_df = None
        self.selected_features = {}
        self.best_models = {}
        self.final_predictions = {}
        
    def load_updated_data(self):
        """加载更新后的数据"""
        print("=== 加载更新后的数据 ===")
        
        # 读取更新后的数据
        df = pd.read_csv('updated_lottery_data.csv')
        
        # 重新划分
        self.train_df = df[df['期号'] <= 150].copy()
        self.test_df = df[df['期号'] >= 151].copy()
        
        print(f"训练集：{len(self.train_df)}期 (第{self.train_df['期号'].min()}-{self.train_df['期号'].max()}期)")
        print(f"测试集：{len(self.test_df)}期 (第{self.test_df['期号'].min()}-{self.test_df['期号'].max()}期)")
        
    def create_enhanced_features(self):
        """创建增强特征（复用之前的特征工程逻辑）"""
        print("\n=== 创建增强特征 ===")
        
        # 合并训练和测试数据进行特征工程
        full_df = pd.concat([self.train_df, self.test_df], ignore_index=True)
        full_df = full_df.sort_values('期号').reset_index(drop=True)
        
        print(f"完整数据集：{len(full_df)}期")
        
        # 1. 创建滞后特征
        print("1. 创建滞后特征...")
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6', '特码']
        
        for col in target_cols:
            for lag in [1, 2, 3, 5, 10]:
                full_df[f'{col}_lag{lag}'] = full_df[col].shift(lag)
        
        # 2. 创建滑动窗口统计特征
        print("2. 创建滑动窗口统计特征...")
        
        # 和值
        full_df['和值'] = full_df[['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']].sum(axis=1)
        
        # 奇数个数
        def count_odd(row):
            return sum(1 for x in [row['数字1'], row['数字2'], row['数字3'], 
                                 row['数字4'], row['数字5'], row['数字6']] if x % 2 == 1)
        full_df['奇数个数'] = full_df.apply(count_odd, axis=1)
        
        # 大数个数
        def count_large(row):
            return sum(1 for x in [row['数字1'], row['数字2'], row['数字3'], 
                                 row['数字4'], row['数字5'], row['数字6']] if x > 25)
        full_df['大数个数'] = full_df.apply(count_large, axis=1)
        
        # 滑动窗口统计
        for window in [5, 10, 20]:
            full_df[f'和值_mean_{window}'] = full_df['和值'].rolling(window=window).mean()
            full_df[f'和值_std_{window}'] = full_df['和值'].rolling(window=window).std()
            full_df[f'奇数个数_mean_{window}'] = full_df['奇数个数'].rolling(window=window).mean()
            full_df[f'大数个数_mean_{window}'] = full_df['大数个数'].rolling(window=window).mean()
        
        # 3. 创建间隔特征
        print("3. 创建间隔特征...")
        for num in range(1, 50):
            full_df[f'数字{num}_间隔'] = 0
            last_appearance = -1
            
            for idx, row in full_df.iterrows():
                numbers = [row['数字1'], row['数字2'], row['数字3'], 
                          row['数字4'], row['数字5'], row['数字6']]
                
                if num in numbers:
                    full_df.loc[idx, f'数字{num}_间隔'] = idx - last_appearance if last_appearance >= 0 else 0
                    last_appearance = idx
                else:
                    full_df.loc[idx, f'数字{num}_间隔'] = idx - last_appearance if last_appearance >= 0 else idx + 1
        
        # 4. 创建高级时间序列特征
        print("4. 创建高级时间序列特征...")
        for col in target_cols:
            # 趋势特征
            for window in [5, 10]:
                full_df[f'{col}_trend_{window}'] = full_df[col].rolling(window=window).apply(
                    lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) == window else 0, raw=False)
            
            # 波动性特征
            for window in [5, 10]:
                rolling_mean = full_df[col].rolling(window=window).mean()
                full_df[f'{col}_cv_{window}'] = full_df[col].rolling(window=window).std() / rolling_mean
                full_df[f'{col}_range_{window}'] = (full_df[col].rolling(window=window).max() - 
                                                   full_df[col].rolling(window=window).min())
        
        # 5. 创建特征交互
        print("5. 创建特征交互...")
        # 简化的交互特征
        full_df['和值_奇数个数_交互'] = full_df['和值'] * full_df['奇数个数']
        full_df['和值_大数个数_交互'] = full_df['和值'] * full_df['大数个数']
        
        print(f"特征工程完成，总特征数：{len(full_df.columns)}")
        
        # 重新划分增强后的数据
        self.enhanced_train_df = full_df[full_df['期号'] <= 150].copy()
        self.enhanced_test_df = full_df[full_df['期号'] >= 151].copy()
        
        # 移除NaN值
        print(f"移除NaN前 - 训练集：{len(self.enhanced_train_df)}期，测试集：{len(self.enhanced_test_df)}期")
        self.enhanced_train_df = self.enhanced_train_df.dropna().reset_index(drop=True)
        print(f"移除NaN后 - 训练集：{len(self.enhanced_train_df)}期，测试集：{len(self.enhanced_test_df)}期")
        
        return full_df
    
    def feature_selection(self):
        """特征选择"""
        print("\n=== 特征选择 ===")
        
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        exclude_cols = ['期号', '数字1', '数字2', '数字3', '数字4', '数字5', '数字6', '特码']
        
        feature_cols = [col for col in self.enhanced_train_df.columns if col not in exclude_cols]
        
        for target_col in target_cols:
            print(f"\n为 {target_col} 选择特征...")
            
            # 准备数据
            X = self.enhanced_train_df[feature_cols].fillna(0)
            y = self.enhanced_train_df[target_col]
            
            # 使用互信息进行特征选择
            mi_scores = mutual_info_regression(X, y, random_state=42)
            
            # 选择前30个特征
            feature_importance = pd.DataFrame({
                'feature': feature_cols,
                'importance': mi_scores
            }).sort_values('importance', ascending=False)
            
            selected_features = feature_importance.head(30)['feature'].tolist()
            self.selected_features[target_col] = selected_features
            
            print(f"{target_col} 选择的前10个特征：")
            for i, feature in enumerate(selected_features[:10], 1):
                print(f"  {i}. {feature}")
    
    def train_optimal_models(self):
        """训练最优模型组合"""
        print("\n=== 训练最优模型组合 ===")
        
        # 基于之前的交叉验证结果，使用最优模型组合
        optimal_models = {
            '数字1': 'RandomForest',
            '数字2': 'Ensemble',
            '数字3': 'NeuralNetwork', 
            '数字4': 'Ensemble',
            '数字5': 'RandomForest',
            '数字6': 'Ensemble'
        }
        
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        for target_col in target_cols:
            print(f"\n训练 {target_col} 的最优模型...")
            
            model_name = optimal_models[target_col]
            
            # 准备数据
            features = self.selected_features[target_col]
            X_train = self.enhanced_train_df[features].fillna(0).values
            y_train = self.enhanced_train_df[target_col].values
            X_test = self.enhanced_test_df[features].fillna(0).values
            y_test = self.enhanced_test_df[target_col].values
            
            # 创建模型
            if model_name == 'RandomForest':
                model = RandomForestRegressor(n_estimators=100, max_depth=10, random_state=42, n_jobs=-1)
            elif model_name == 'NeuralNetwork':
                model = MLPRegressor(hidden_layer_sizes=(100, 50), max_iter=1000, random_state=42)
                # 标准化数据
                scaler = StandardScaler()
                X_train = scaler.fit_transform(X_train)
                X_test = scaler.transform(X_test)
            elif model_name == 'Ensemble':
                model = VotingRegressor([
                    ('rf', RandomForestRegressor(n_estimators=50, max_depth=8, random_state=42)),
                    ('gb', GradientBoostingRegressor(n_estimators=50, max_depth=4, random_state=42)),
                    ('ridge', Ridge(alpha=0.5))
                ])
            
            # 训练模型
            model.fit(X_train, y_train)
            
            # 预测
            y_pred_train = model.predict(X_train)
            y_pred_test = model.predict(X_test)
            
            # 评估
            train_mse = mean_squared_error(y_train, y_pred_train)
            test_mse = mean_squared_error(y_test, y_pred_test)
            train_mae = mean_absolute_error(y_train, y_pred_train)
            test_mae = mean_absolute_error(y_test, y_pred_test)
            
            # 保存结果
            self.best_models[target_col] = {
                'model': model,
                'model_name': model_name,
                'features': features
            }
            
            self.final_predictions[target_col] = {
                'model_name': model_name,
                'train_predictions': y_pred_train,
                'test_predictions': y_pred_test,
                'actual_test': y_test,
                'train_mse': train_mse,
                'test_mse': test_mse,
                'train_mae': train_mae,
                'test_mae': test_mae
            }
            
            print(f"  模型: {model_name}")
            print(f"  训练MSE: {train_mse:.2f}, 测试MSE: {test_mse:.2f}")
            print(f"  训练MAE: {train_mae:.2f}, 测试MAE: {test_mae:.2f}")
    
    def evaluate_updated_model_performance(self):
        """评估更新后的模型性能"""
        print("\n=== 评估更新后的模型性能 ===")
        
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        # 收集所有预测结果
        all_predictions = []
        all_actuals = []
        
        for i in range(len(self.enhanced_test_df)):
            period_pred = []
            period_actual = []
            
            for col in target_cols:
                if col in self.final_predictions:
                    pred_val = int(np.round(np.clip(
                        self.final_predictions[col]['test_predictions'][i], 1, 49)))
                    actual_val = self.final_predictions[col]['actual_test'][i]
                    
                    period_pred.append(pred_val)
                    period_actual.append(actual_val)
            
            if len(period_pred) == 6:
                all_predictions.append(period_pred)
                all_actuals.append(period_actual)
        
        # 计算预测准确性
        correct_per_period = []
        for pred, actual in zip(all_predictions, all_actuals):
            pred_set = set(pred)
            actual_set = set(actual)
            correct_count = len(pred_set.intersection(actual_set))
            correct_per_period.append(correct_count)
        
        if correct_per_period:
            avg_correct = np.mean(correct_per_period)
            max_correct = max(correct_per_period)
            
            print(f"更新后模型性能（151-170期测试）：")
            print(f"平均每期预测正确数字个数: {avg_correct:.3f}")
            print(f"最好单期预测: {max_correct} 个数字")
            print(f"预测正确率分布: {np.bincount(correct_per_period)}")
            
            # 详细预测结果
            print(f"\n详细预测结果：")
            for i, (pred, actual, correct) in enumerate(zip(all_predictions, all_actuals, correct_per_period)):
                period_num = self.enhanced_test_df.iloc[i]['期号']
                print(f"第{period_num}期: 预测{pred} vs 实际{actual} -> 正确{correct}个")
            
            return {
                'avg_correct': avg_correct,
                'max_correct': max_correct,
                'correct_distribution': correct_per_period,
                'all_predictions': all_predictions,
                'all_actuals': all_actuals,
                'detailed_results': list(zip(all_predictions, all_actuals, correct_per_period))
            }
        else:
            print("评估失败：没有有效的预测结果")
            return None
    
    def run_updated_model_training(self):
        """运行完整的模型重新训练流程"""
        print("开始模型重新训练")
        print("=" * 60)
        
        # 1. 加载更新后的数据
        self.load_updated_data()
        
        # 2. 创建增强特征
        self.create_enhanced_features()
        
        # 3. 特征选择
        self.feature_selection()
        
        # 4. 训练最优模型
        self.train_optimal_models()
        
        # 5. 评估性能
        performance_results = self.evaluate_updated_model_performance()
        
        print("\n" + "=" * 60)
        print("✅ 模型重新训练完成！")
        
        if performance_results:
            print(f"🎯 新测试集性能: {performance_results['avg_correct']:.3f} 个数字/期")
            print(f"🏆 最佳单期: {performance_results['max_correct']} 个数字")
        
        return {
            'best_models': self.best_models,
            'final_predictions': self.final_predictions,
            'performance_results': performance_results,
            'enhanced_train_df': self.enhanced_train_df,
            'enhanced_test_df': self.enhanced_test_df
        }

if __name__ == "__main__":
    trainer = UpdatedModelTraining()
    results = trainer.run_updated_model_training()
