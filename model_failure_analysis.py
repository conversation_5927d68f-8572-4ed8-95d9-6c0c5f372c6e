#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
当前模型失败原因深度分析
分析随机森林模型表现不佳的具体原因
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error
from sklearn.inspection import permutation_importance
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class ModelFailureAnalyzer:
    def __init__(self):
        self.df = pd.read_csv('lottery_data.csv')
        self.train_df = None
        self.test_df = None
        self.features_df = None
        
    def load_processed_data(self):
        """加载处理后的数据"""
        from lottery_analysis import LotteryAnalyzer
        analyzer = LotteryAnalyzer()
        analyzer.load_and_clean_data()
        self.features_df = analyzer.feature_engineering()
        self.train_df, self.test_df = analyzer.split_dataset()
        
    def analyze_data_distribution(self):
        """分析数据分布特征"""
        print("=== 1. 数据分布分析 ===")
        
        # 分析目标变量的分布
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6', '特码']
        
        fig, axes = plt.subplots(2, 4, figsize=(20, 10))
        axes = axes.flatten()
        
        for i, col in enumerate(target_cols):
            # 绘制分布直方图
            axes[i].hist(self.df[col], bins=20, alpha=0.7, edgecolor='black')
            axes[i].set_title(f'{col} 分布')
            axes[i].set_xlabel('数值')
            axes[i].set_ylabel('频次')
            
            # 计算分布统计
            mean_val = self.df[col].mean()
            std_val = self.df[col].std()
            skew_val = stats.skew(self.df[col])
            
            axes[i].axvline(mean_val, color='red', linestyle='--', label=f'均值: {mean_val:.1f}')
            axes[i].legend()
            
            print(f"{col}: 均值={mean_val:.2f}, 标准差={std_val:.2f}, 偏度={skew_val:.3f}")
        
        # 移除多余的子图
        axes[7].remove()
        
        plt.tight_layout()
        plt.savefig('target_distribution_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 分析数字之间的相关性
        print("\n数字位置间的相关性分析：")
        correlation_matrix = self.df[target_cols[:-1]].corr()  # 排除特码
        print(correlation_matrix)
        
        plt.figure(figsize=(8, 6))
        sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0)
        plt.title('数字位置间相关性热力图')
        plt.tight_layout()
        plt.savefig('correlation_heatmap.png', dpi=300, bbox_inches='tight')
        plt.show()
        
    def analyze_feature_effectiveness(self):
        """分析特征有效性"""
        print("\n=== 2. 特征有效性分析 ===")
        
        if self.features_df is None:
            self.load_processed_data()
        
        # 准备特征数据
        exclude_cols = ['期号', '数字1', '数字2', '数字3', '数字4', '数字5', '数字6', '特码']
        feature_cols = [col for col in self.features_df.columns if col not in exclude_cols]
        
        # 移除包含过多间隔特征
        selected_features = []
        for col in feature_cols:
            if '间隔' in col:
                important_numbers = [1, 3, 5, 10, 15, 20, 25, 30, 35, 40, 45]
                if any(f'数字{num}_间隔' == col for num in important_numbers):
                    selected_features.append(col)
            else:
                selected_features.append(col)
        
        X_train = self.train_df[selected_features].fillna(0)
        
        # 分析特征与目标变量的相关性
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        feature_correlations = {}
        for target_col in target_cols:
            y_train = self.train_df[target_col]
            correlations = []
            
            for feature in selected_features:
                if X_train[feature].std() > 0:  # 避免常数特征
                    corr = np.corrcoef(X_train[feature], y_train)[0, 1]
                    if not np.isnan(corr):
                        correlations.append((feature, abs(corr)))
            
            correlations.sort(key=lambda x: x[1], reverse=True)
            feature_correlations[target_col] = correlations[:10]
            
            print(f"\n{target_col} 最相关的10个特征：")
            for feature, corr in correlations[:10]:
                print(f"  {feature}: {corr:.4f}")
        
        return feature_correlations
    
    def analyze_model_predictions(self):
        """分析模型预测结果"""
        print("\n=== 3. 模型预测分析 ===")
        
        if self.features_df is None:
            self.load_processed_data()
        
        # 重新训练模型并分析预测
        exclude_cols = ['期号', '数字1', '数字2', '数字3', '数字4', '数字5', '数字6', '特码']
        feature_cols = [col for col in self.features_df.columns if col not in exclude_cols]
        
        # 特征选择
        selected_features = []
        for col in feature_cols:
            if '间隔' in col:
                important_numbers = [1, 3, 5, 10, 15, 20, 25, 30, 35, 40, 45]
                if any(f'数字{num}_间隔' == col for num in important_numbers):
                    selected_features.append(col)
            else:
                selected_features.append(col)
        
        X_train = self.train_df[selected_features].fillna(0)
        X_test = self.test_df[selected_features].fillna(0)
        
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        prediction_analysis = {}
        
        for target_col in target_cols:
            y_train = self.train_df[target_col]
            y_test = self.test_df[target_col]
            
            # 训练随机森林
            rf = RandomForestRegressor(n_estimators=100, max_depth=10, random_state=42)
            rf.fit(X_train, y_train)
            
            # 预测
            y_pred_train = rf.predict(X_train)
            y_pred_test = rf.predict(X_test)
            
            # 分析预测误差
            train_mse = mean_squared_error(y_train, y_pred_train)
            test_mse = mean_squared_error(y_test, y_pred_test)
            
            train_mae = mean_absolute_error(y_train, y_pred_train)
            test_mae = mean_absolute_error(y_test, y_pred_test)
            
            # 分析预测值分布
            pred_mean = np.mean(y_pred_test)
            pred_std = np.std(y_pred_test)
            actual_mean = np.mean(y_test)
            actual_std = np.std(y_test)
            
            prediction_analysis[target_col] = {
                'train_mse': train_mse,
                'test_mse': test_mse,
                'train_mae': train_mae,
                'test_mae': test_mae,
                'pred_mean': pred_mean,
                'pred_std': pred_std,
                'actual_mean': actual_mean,
                'actual_std': actual_std,
                'overfitting_ratio': test_mse / train_mse if train_mse > 0 else float('inf')
            }
            
            print(f"\n{target_col} 预测分析：")
            print(f"  训练MSE: {train_mse:.2f}, 测试MSE: {test_mse:.2f}")
            print(f"  训练MAE: {train_mae:.2f}, 测试MAE: {test_mae:.2f}")
            print(f"  过拟合比率: {test_mse/train_mse:.2f}")
            print(f"  预测均值: {pred_mean:.2f}, 实际均值: {actual_mean:.2f}")
            print(f"  预测标准差: {pred_std:.2f}, 实际标准差: {actual_std:.2f}")
        
        return prediction_analysis
    
    def analyze_randomness_challenge(self):
        """分析随机性挑战"""
        print("\n=== 4. 随机性挑战分析 ===")
        
        # 计算理论预测难度
        total_numbers = 49
        selected_numbers = 6
        
        # 单个位置的预测难度
        single_position_accuracy = 1 / total_numbers
        print(f"单个位置理论最高准确率: {single_position_accuracy:.4f}")
        
        # 分析数字出现的随机性
        all_numbers = []
        for col in ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']:
            all_numbers.extend(self.df[col].tolist())
        
        # 卡方检验
        from collections import Counter
        number_counts = Counter(all_numbers)
        observed_freq = [number_counts.get(i, 0) for i in range(1, 50)]
        expected_freq = len(all_numbers) / 49
        
        chi_square = sum((obs - expected_freq) ** 2 / expected_freq for obs in observed_freq)
        p_value = 1 - stats.chi2.cdf(chi_square, df=48)
        
        print(f"卡方统计量: {chi_square:.2f}")
        print(f"p值: {p_value:.4f}")
        print(f"随机性评估: {'接近随机分布' if p_value > 0.05 else '偏离随机分布'}")
        
        # 分析序列的自相关性
        print("\n序列自相关性分析：")
        for col in ['数字1', '数字2', '数字3']:
            series = self.df[col].values
            autocorr_1 = np.corrcoef(series[:-1], series[1:])[0, 1]
            autocorr_2 = np.corrcoef(series[:-2], series[2:])[0, 1]
            
            print(f"{col} - 滞后1期自相关: {autocorr_1:.4f}, 滞后2期自相关: {autocorr_2:.4f}")
    
    def generate_failure_report(self):
        """生成失败原因报告"""
        print("\n" + "="*60)
        print("模型失败原因综合分析报告")
        print("="*60)
        
        self.load_processed_data()
        
        # 执行各项分析
        self.analyze_data_distribution()
        feature_corr = self.analyze_feature_effectiveness()
        pred_analysis = self.analyze_model_predictions()
        self.analyze_randomness_challenge()
        
        print("\n=== 失败原因总结 ===")
        print("1. 数据本质随机性：")
        print("   - 彩票数字设计为随机分布，历史模式对未来预测价值有限")
        print("   - 各数字位置间相关性极低，难以建立有效的预测关系")
        
        print("\n2. 特征工程局限性：")
        print("   - 滞后特征与目标变量相关性普遍较低（<0.1）")
        print("   - 统计特征（和值、奇偶比等）无法捕捉个体数字的变化模式")
        print("   - 间隔特征在随机环境下缺乏预测价值")
        
        print("\n3. 模型适配性问题：")
        print("   - 随机森林适合捕捉非线性关系，但在纯随机数据上效果有限")
        print("   - 模型倾向于预测均值附近的值，缺乏对极值的预测能力")
        print("   - 过拟合现象明显，测试误差远大于训练误差")
        
        print("\n4. 评估指标挑战：")
        print("   - 精确匹配要求过高，在随机环境下成功率极低")
        print("   - 需要考虑更宽松的评估标准或概率预测方法")
        
        return {
            'feature_correlations': feature_corr,
            'prediction_analysis': pred_analysis
        }

if __name__ == "__main__":
    analyzer = ModelFailureAnalyzer()
    results = analyzer.generate_failure_report()
