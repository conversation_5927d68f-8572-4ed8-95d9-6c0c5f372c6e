#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
彩票预测改进方法
尝试不同的建模思路和评估方法
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import cross_val_score
from sklearn.metrics import classification_report
import random
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class ImprovedLotteryPredictor:
    def __init__(self):
        self.df = pd.read_csv('lottery_data.csv')
        
    def alternative_approach_1_hot_cold_strategy(self):
        """方法1：基于冷热号的策略"""
        print("=== 方法1：冷热号策略 ===")
        
        # 计算最近20期的号码频率
        recent_data = self.df.tail(20)
        all_numbers = []
        for col in ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']:
            all_numbers.extend(recent_data[col].tolist())
        
        number_counts = Counter(all_numbers)
        
        # 热号策略：选择最频繁的数字
        hot_numbers = [num for num, count in number_counts.most_common(10)]
        
        # 冷号策略：选择最不频繁的数字
        all_possible = list(range(1, 50))
        cold_numbers = [num for num in all_possible if number_counts.get(num, 0) <= 1]
        
        print(f"热号前10：{hot_numbers}")
        print(f"冷号：{cold_numbers[:10]}")
        
        # 在测试集上评估
        test_data = self.df.tail(20)
        hot_correct = 0
        cold_correct = 0
        total_numbers = 0
        
        for _, row in test_data.iterrows():
            actual = [row['数字1'], row['数字2'], row['数字3'], 
                     row['数字4'], row['数字5'], row['数字6']]
            
            hot_matches = len(set(actual).intersection(set(hot_numbers[:6])))
            cold_matches = len(set(actual).intersection(set(cold_numbers[:6])))
            
            hot_correct += hot_matches
            cold_correct += cold_matches
            total_numbers += 6
        
        print(f"热号策略命中率：{hot_correct/total_numbers:.3f}")
        print(f"冷号策略命中率：{cold_correct/total_numbers:.3f}")
        
    def alternative_approach_2_pattern_based(self):
        """方法2：基于模式的预测"""
        print("\n=== 方法2：模式预测 ===")
        
        # 分析和值模式
        sum_values = []
        for _, row in self.df.iterrows():
            sum_val = sum([row['数字1'], row['数字2'], row['数字3'], 
                          row['数字4'], row['数字5'], row['数字6']])
            sum_values.append(sum_val)
        
        # 预测下一期和值应该在均值±1个标准差范围内
        mean_sum = np.mean(sum_values)
        std_sum = np.std(sum_values)
        predicted_range = (mean_sum - std_sum, mean_sum + std_sum)
        
        print(f"历史和值均值：{mean_sum:.1f}")
        print(f"预测下期和值范围：{predicted_range[0]:.1f} - {predicted_range[1]:.1f}")
        
        # 基于和值范围生成候选组合
        def generate_combinations_by_sum(target_sum_range, num_combinations=100):
            combinations = []
            for _ in range(num_combinations):
                while True:
                    numbers = sorted(random.sample(range(1, 50), 6))
                    if target_sum_range[0] <= sum(numbers) <= target_sum_range[1]:
                        combinations.append(numbers)
                        break
            return combinations
        
        candidates = generate_combinations_by_sum(predicted_range, 50)
        print(f"生成了{len(candidates)}个符合和值范围的候选组合")
        
    def alternative_approach_3_ensemble_voting(self):
        """方法3：集成投票策略"""
        print("\n=== 方法3：集成投票策略 ===")
        
        # 收集多种策略的预测
        strategies = {}
        
        # 策略1：最近频率
        recent_20 = self.df.tail(20)
        recent_numbers = []
        for col in ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']:
            recent_numbers.extend(recent_20[col].tolist())
        recent_counts = Counter(recent_numbers)
        strategies['recent_freq'] = [num for num, _ in recent_counts.most_common(15)]
        
        # 策略2：历史频率
        all_numbers = []
        for col in ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']:
            all_numbers.extend(self.df[col].tolist())
        all_counts = Counter(all_numbers)
        strategies['historical_freq'] = [num for num, _ in all_counts.most_common(15)]
        
        # 策略3：间隔分析
        def get_interval_candidates():
            # 找出很久没出现的数字
            last_appearance = {}
            for num in range(1, 50):
                last_appearance[num] = -1
            
            for idx, row in self.df.iterrows():
                numbers = [row['数字1'], row['数字2'], row['数字3'], 
                          row['数字4'], row['数字5'], row['数字6']]
                for num in numbers:
                    last_appearance[num] = idx
            
            # 选择最久没出现的数字
            current_idx = len(self.df) - 1
            intervals = [(num, current_idx - last_idx) for num, last_idx in last_appearance.items()]
            intervals.sort(key=lambda x: x[1], reverse=True)
            
            return [num for num, _ in intervals[:15]]
        
        strategies['interval'] = get_interval_candidates()
        
        # 投票机制
        vote_counts = Counter()
        for strategy_name, candidates in strategies.items():
            for num in candidates:
                vote_counts[num] += 1
        
        # 选择得票最多的数字
        ensemble_prediction = [num for num, _ in vote_counts.most_common(10)]
        
        print("各策略候选数字：")
        for name, candidates in strategies.items():
            print(f"{name}: {candidates[:8]}")
        
        print(f"\n集成预测前10：{ensemble_prediction}")
        
        # 在测试集上评估集成策略
        test_data = self.df.tail(10)  # 最后10期作为测试
        ensemble_correct = 0
        total_test_numbers = 0
        
        for _, row in test_data.iterrows():
            actual = [row['数字1'], row['数字2'], row['数字3'], 
                     row['数字4'], row['数字5'], row['数字6']]
            
            matches = len(set(actual).intersection(set(ensemble_prediction)))
            ensemble_correct += matches
            total_test_numbers += 6
        
        print(f"集成策略在最后10期的命中率：{ensemble_correct/total_test_numbers:.3f}")
        
    def probability_based_prediction(self):
        """方法4：基于概率的预测"""
        print("\n=== 方法4：概率预测 ===")
        
        # 计算每个数字的出现概率
        all_numbers = []
        for col in ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']:
            all_numbers.extend(self.df[col].tolist())
        
        total_draws = len(all_numbers)
        probabilities = {}
        
        for num in range(1, 50):
            count = all_numbers.count(num)
            probabilities[num] = count / total_draws
        
        # 按概率排序
        sorted_probs = sorted(probabilities.items(), key=lambda x: x[1], reverse=True)
        
        print("数字出现概率前10：")
        for num, prob in sorted_probs[:10]:
            print(f"数字{num}: {prob:.3f}")
        
        # 基于概率的加权随机选择
        def weighted_random_selection(probabilities, n=6):
            numbers = list(probabilities.keys())
            weights = list(probabilities.values())
            selected = np.random.choice(numbers, size=n, replace=False, p=weights)
            return sorted(selected)
        
        # 生成多个概率预测
        prob_predictions = []
        for _ in range(5):
            pred = weighted_random_selection(probabilities)
            prob_predictions.append(pred)
        
        print("\n基于概率的5个预测组合：")
        for i, pred in enumerate(prob_predictions, 1):
            print(f"预测{i}: {pred}")
    
    def run_all_approaches(self):
        """运行所有改进方法"""
        print("彩票预测改进方法测试")
        print("=" * 50)
        
        self.alternative_approach_1_hot_cold_strategy()
        self.alternative_approach_2_pattern_based()
        self.alternative_approach_3_ensemble_voting()
        self.probability_based_prediction()
        
        print("\n" + "=" * 50)
        print("总结：")
        print("1. 冷热号策略：基于短期频率，简单直观")
        print("2. 模式预测：基于和值等统计特征")
        print("3. 集成投票：结合多种策略的优势")
        print("4. 概率预测：基于历史概率分布")
        print("\n注意：所有方法都无法保证准确性，")
        print("彩票本质上是随机事件，请理性对待！")

if __name__ == "__main__":
    predictor = ImprovedLotteryPredictor()
    predictor.run_all_approaches()
