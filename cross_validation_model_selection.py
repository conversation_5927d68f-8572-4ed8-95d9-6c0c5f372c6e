#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交叉验证与模型选择
使用时间序列交叉验证确保模型稳定性，选择最优模型组合
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.model_selection import TimeSeriesSplit, cross_val_score
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, VotingRegressor
from sklearn.neural_network import MLPRegressor
from sklearn.linear_model import Ridge
from sklearn.metrics import mean_squared_error, mean_absolute_error
from sklearn.preprocessing import StandardScaler
import xgboost as xgb
from statsmodels.tsa.arima.model import ARIMA
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class CrossValidationModelSelection:
    def __init__(self):
        self.models = {}
        self.cv_results = {}
        self.best_models = {}
        self.final_predictions = {}
        
    def load_enhanced_features(self):
        """加载增强特征数据"""
        from advanced_feature_engineering import AdvancedFeatureEngineer
        engineer = AdvancedFeatureEngineer()
        results = engineer.run_advanced_feature_engineering()
        
        self.train_df = results['train_df']
        self.test_df = results['test_df']
        self.selected_features = results['selected_features']
        
        print(f"加载数据完成 - 训练集：{len(self.train_df)}期，测试集：{len(self.test_df)}期")
        
    def prepare_models(self):
        """准备候选模型"""
        print("\n=== 准备候选模型 ===")
        
        self.models = {
            'XGBoost': xgb.XGBRegressor(
                n_estimators=100, max_depth=5, learning_rate=0.1,
                random_state=42, n_jobs=-1
            ),
            'RandomForest': RandomForestRegressor(
                n_estimators=100, max_depth=10, random_state=42, n_jobs=-1
            ),
            'GradientBoosting': GradientBoostingRegressor(
                n_estimators=100, max_depth=5, learning_rate=0.1, random_state=42
            ),
            'NeuralNetwork': MLPRegressor(
                hidden_layer_sizes=(100, 50), max_iter=1000, random_state=42
            ),
            'Ridge': Ridge(alpha=1.0),
            'Ensemble': VotingRegressor([
                ('rf', RandomForestRegressor(n_estimators=50, max_depth=8, random_state=42)),
                ('gb', GradientBoostingRegressor(n_estimators=50, max_depth=4, random_state=42)),
                ('ridge', Ridge(alpha=0.5))
            ])
        }
        
        print(f"准备了 {len(self.models)} 个候选模型")
        
    def time_series_cross_validation(self, target_col, n_splits=5):
        """时间序列交叉验证"""
        print(f"\n=== {target_col} 时间序列交叉验证 ===")
        
        # 获取特征和目标
        features = self.selected_features[target_col]
        X = self.train_df[features].fillna(0).values
        y = self.train_df[target_col].values
        
        # 时间序列分割
        tscv = TimeSeriesSplit(n_splits=n_splits)
        
        cv_scores = {}
        
        for model_name, model in self.models.items():
            print(f"  评估 {model_name}...")
            
            fold_scores = []
            fold_predictions = []
            fold_actuals = []
            
            for fold, (train_idx, val_idx) in enumerate(tscv.split(X)):
                X_train_fold, X_val_fold = X[train_idx], X[val_idx]
                y_train_fold, y_val_fold = y[train_idx], y[val_idx]
                
                # 对神经网络进行标准化
                if model_name == 'NeuralNetwork':
                    scaler = StandardScaler()
                    X_train_fold = scaler.fit_transform(X_train_fold)
                    X_val_fold = scaler.transform(X_val_fold)
                
                try:
                    # 训练模型
                    model.fit(X_train_fold, y_train_fold)
                    
                    # 预测
                    y_pred = model.predict(X_val_fold)
                    
                    # 计算分数
                    mse = mean_squared_error(y_val_fold, y_pred)
                    mae = mean_absolute_error(y_val_fold, y_pred)
                    
                    fold_scores.append({'mse': mse, 'mae': mae})
                    fold_predictions.extend(y_pred)
                    fold_actuals.extend(y_val_fold)
                    
                except Exception as e:
                    print(f"    Fold {fold+1} 失败: {str(e)}")
                    fold_scores.append({'mse': float('inf'), 'mae': float('inf')})
            
            # 计算平均分数
            if fold_scores:
                avg_mse = np.mean([score['mse'] for score in fold_scores if score['mse'] != float('inf')])
                avg_mae = np.mean([score['mae'] for score in fold_scores if score['mae'] != float('inf')])
                std_mse = np.std([score['mse'] for score in fold_scores if score['mse'] != float('inf')])
                std_mae = np.std([score['mae'] for score in fold_scores if score['mae'] != float('inf')])
                
                cv_scores[model_name] = {
                    'avg_mse': avg_mse,
                    'std_mse': std_mse,
                    'avg_mae': avg_mae,
                    'std_mae': std_mae,
                    'fold_scores': fold_scores,
                    'predictions': fold_predictions,
                    'actuals': fold_actuals
                }
                
                print(f"    MSE: {avg_mse:.2f} ± {std_mse:.2f}")
                print(f"    MAE: {avg_mae:.2f} ± {std_mae:.2f}")
        
        return cv_scores
    
    def select_best_models(self):
        """选择最佳模型"""
        print("\n=== 模型选择结果 ===")
        
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        for target_col in target_cols:
            print(f"\n{target_col} 最佳模型选择:")
            
            # 进行交叉验证
            cv_results = self.time_series_cross_validation(target_col)
            self.cv_results[target_col] = cv_results
            
            # 选择最佳模型（基于MSE）
            best_model_name = min(cv_results.keys(), 
                                key=lambda x: cv_results[x]['avg_mse'])
            
            self.best_models[target_col] = {
                'name': best_model_name,
                'model': self.models[best_model_name],
                'cv_score': cv_results[best_model_name]
            }
            
            print(f"  最佳模型: {best_model_name}")
            print(f"  CV MSE: {cv_results[best_model_name]['avg_mse']:.2f} ± {cv_results[best_model_name]['std_mse']:.2f}")
            print(f"  CV MAE: {cv_results[best_model_name]['avg_mae']:.2f} ± {cv_results[best_model_name]['std_mae']:.2f}")
    
    def train_final_models(self):
        """训练最终模型"""
        print("\n=== 训练最终模型 ===")
        
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        for target_col in target_cols:
            print(f"\n训练 {target_col} 的最终模型...")
            
            best_model_info = self.best_models[target_col]
            model = best_model_info['model']
            
            # 准备数据
            features = self.selected_features[target_col]
            X_train = self.train_df[features].fillna(0).values
            y_train = self.train_df[target_col].values
            X_test = self.test_df[features].fillna(0).values
            y_test = self.test_df[target_col].values
            
            # 对神经网络进行标准化
            if best_model_info['name'] == 'NeuralNetwork':
                scaler = StandardScaler()
                X_train = scaler.fit_transform(X_train)
                X_test = scaler.transform(X_test)
            
            # 训练模型
            model.fit(X_train, y_train)
            
            # 预测
            y_pred_train = model.predict(X_train)
            y_pred_test = model.predict(X_test)
            
            # 评估
            train_mse = mean_squared_error(y_train, y_pred_train)
            test_mse = mean_squared_error(y_test, y_pred_test)
            train_mae = mean_absolute_error(y_train, y_pred_train)
            test_mae = mean_absolute_error(y_test, y_pred_test)
            
            self.final_predictions[target_col] = {
                'model_name': best_model_info['name'],
                'train_predictions': y_pred_train,
                'test_predictions': y_pred_test,
                'actual_test': y_test,
                'train_mse': train_mse,
                'test_mse': test_mse,
                'train_mae': train_mae,
                'test_mae': test_mae
            }
            
            print(f"  模型: {best_model_info['name']}")
            print(f"  训练MSE: {train_mse:.2f}, 测试MSE: {test_mse:.2f}")
            print(f"  训练MAE: {train_mae:.2f}, 测试MAE: {test_mae:.2f}")
    
    def evaluate_final_lottery_prediction(self):
        """评估最终彩票预测性能"""
        print("\n=== 最终彩票预测性能评估 ===")
        
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        # 收集所有预测结果
        all_predictions = []
        all_actuals = []
        
        for i in range(len(self.test_df)):
            period_pred = []
            period_actual = []
            
            for col in target_cols:
                if col in self.final_predictions:
                    pred_val = int(np.round(np.clip(
                        self.final_predictions[col]['test_predictions'][i], 1, 49)))
                    actual_val = self.final_predictions[col]['actual_test'][i]
                    
                    period_pred.append(pred_val)
                    period_actual.append(actual_val)
            
            if len(period_pred) == 6:
                all_predictions.append(period_pred)
                all_actuals.append(period_actual)
        
        # 计算预测准确性
        correct_per_period = []
        for pred, actual in zip(all_predictions, all_actuals):
            pred_set = set(pred)
            actual_set = set(actual)
            correct_count = len(pred_set.intersection(actual_set))
            correct_per_period.append(correct_count)
        
        if correct_per_period:
            avg_correct = np.mean(correct_per_period)
            max_correct = max(correct_per_period)
            
            print(f"最终模型组合预测结果：")
            print(f"平均每期预测正确数字个数: {avg_correct:.3f}")
            print(f"最好单期预测: {max_correct} 个数字")
            print(f"预测正确率分布: {np.bincount(correct_per_period)}")
            
            # 详细分析
            print(f"\n详细分析:")
            print(f"目标性能: 0.60 个数字/期")
            print(f"实际性能: {avg_correct:.3f} 个数字/期")
            print(f"性能差距: {0.60 - avg_correct:.3f} 个数字/期")
            
            if avg_correct >= 0.60:
                print("✅ 达到目标性能！")
            else:
                print("❌ 未达到目标性能")
                improvement_needed = (0.60 - avg_correct) / avg_correct * 100
                print(f"需要提升 {improvement_needed:.1f}% 的性能")
            
            return {
                'avg_correct': avg_correct,
                'max_correct': max_correct,
                'correct_distribution': correct_per_period,
                'target_achieved': avg_correct >= 0.60,
                'all_predictions': all_predictions,
                'all_actuals': all_actuals
            }
        else:
            print("评估失败：没有有效的预测结果")
            return None
    
    def plot_model_comparison(self):
        """绘制模型比较图"""
        print("\n=== 绘制模型比较图 ===")
        
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        axes = axes.flatten()
        
        for i, target_col in enumerate(target_cols):
            if target_col in self.cv_results:
                cv_results = self.cv_results[target_col]
                
                model_names = list(cv_results.keys())
                mse_means = [cv_results[name]['avg_mse'] for name in model_names]
                mse_stds = [cv_results[name]['std_mse'] for name in model_names]
                
                # 绘制误差条形图
                bars = axes[i].bar(model_names, mse_means, yerr=mse_stds, 
                                 capsize=5, alpha=0.7)
                
                # 标记最佳模型
                best_idx = mse_means.index(min(mse_means))
                bars[best_idx].set_color('red')
                bars[best_idx].set_alpha(1.0)
                
                axes[i].set_title(f'{target_col} - 模型交叉验证MSE比较')
                axes[i].set_ylabel('MSE')
                axes[i].tick_params(axis='x', rotation=45)
                axes[i].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('model_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def run_cross_validation_selection(self):
        """运行完整的交叉验证和模型选择"""
        print("开始交叉验证与模型选择")
        print("=" * 60)
        
        # 1. 加载数据
        self.load_enhanced_features()
        
        # 2. 准备模型
        self.prepare_models()
        
        # 3. 选择最佳模型
        self.select_best_models()
        
        # 4. 训练最终模型
        self.train_final_models()
        
        # 5. 评估最终性能
        final_evaluation = self.evaluate_final_lottery_prediction()
        
        # 6. 绘制比较图
        self.plot_model_comparison()
        
        print("\n" + "=" * 60)
        print("交叉验证与模型选择完成！")
        
        return {
            'cv_results': self.cv_results,
            'best_models': self.best_models,
            'final_predictions': self.final_predictions,
            'final_evaluation': final_evaluation
        }

if __name__ == "__main__":
    cv_selector = CrossValidationModelSelection()
    results = cv_selector.run_cross_validation_selection()
