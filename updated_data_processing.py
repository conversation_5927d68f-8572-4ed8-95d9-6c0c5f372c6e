#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据更新与重新划分
读取更新后的数据集.txt文件（1-170期），重新划分为训练集（1-150期）和测试集（151-170期）
"""

import pandas as pd
import numpy as np
import re
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class UpdatedDataProcessor:
    def __init__(self):
        self.df = None
        self.train_df = None
        self.test_df = None
        
    def parse_updated_data(self):
        """解析更新后的数据集.txt文件"""
        print("=== 解析更新后的数据集 ===")
        
        data = []
        
        with open('数据集.txt', 'r', encoding='utf-8') as file:
            lines = file.readlines()
        
        print(f"读取到 {len(lines)} 行数据")
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 使用正则表达式解析每行数据
            # 格式：170 期：22，20，36，44，31，19，特码是 33
            match = re.match(r'(\d+)\s*期：(.+?)，特码是\s*(\d+)', line)
            
            if match:
                period = int(match.group(1))
                numbers_str = match.group(2)
                special_code = int(match.group(3))
                
                # 解析6个正常数字
                numbers = [int(x.strip()) for x in numbers_str.split('，')]
                
                if len(numbers) == 6:
                    data.append({
                        '期号': period,
                        '数字1': numbers[0],
                        '数字2': numbers[1], 
                        '数字3': numbers[2],
                        '数字4': numbers[3],
                        '数字5': numbers[4],
                        '数字6': numbers[5],
                        '特码': special_code
                    })
                else:
                    print(f"警告：第{period}期数字数量不正确: {numbers}")
            else:
                print(f"警告：无法解析行: {line}")
        
        # 创建DataFrame并按期号排序
        self.df = pd.DataFrame(data)
        self.df = self.df.sort_values('期号').reset_index(drop=True)
        
        print(f"成功解析 {len(self.df)} 期数据")
        print(f"期号范围: {self.df['期号'].min()} - {self.df['期号'].max()}")
        
        # 验证数据完整性
        expected_periods = set(range(1, 171))  # 1-170期
        actual_periods = set(self.df['期号'].tolist())
        missing_periods = expected_periods - actual_periods
        
        if missing_periods:
            print(f"警告：缺失期号: {sorted(missing_periods)}")
        else:
            print("✅ 数据完整性检查通过")
        
        return self.df
    
    def split_updated_dataset(self):
        """重新划分数据集：训练集1-150期，测试集151-170期"""
        print("\n=== 重新划分数据集 ===")
        
        if self.df is None:
            raise ValueError("请先解析数据")
        
        # 训练集：1-150期
        self.train_df = self.df[self.df['期号'] <= 150].copy()
        
        # 测试集：151-170期
        self.test_df = self.df[self.df['期号'] >= 151].copy()
        
        print(f"训练集：{len(self.train_df)}期 (第{self.train_df['期号'].min()}-{self.train_df['期号'].max()}期)")
        print(f"测试集：{len(self.test_df)}期 (第{self.test_df['期号'].min()}-{self.test_df['期号'].max()}期)")
        
        # 验证划分
        if len(self.train_df) != 150:
            print(f"警告：训练集期数不正确，期望150期，实际{len(self.train_df)}期")
        if len(self.test_df) != 20:
            print(f"警告：测试集期数不正确，期望20期，实际{len(self.test_df)}期")
        
        # 保存更新后的数据
        self.df.to_csv('updated_lottery_data.csv', index=False, encoding='utf-8')
        print("✅ 更新后的完整数据已保存为 updated_lottery_data.csv")
        
        return self.train_df, self.test_df
    
    def compare_with_previous_data(self):
        """与之前的数据进行对比"""
        print("\n=== 数据对比分析 ===")
        
        # 读取之前的数据
        try:
            old_df = pd.read_csv('lottery_data.csv')
            print(f"之前数据：{len(old_df)}期")
            print(f"更新数据：{len(self.df)}期")
            print(f"新增数据：{len(self.df) - len(old_df)}期")
            
            # 检查数据一致性（前160期）
            if len(old_df) >= 160:
                old_160 = old_df.head(160)
                new_160 = self.df[self.df['期号'] <= 160]
                
                # 比较数据是否一致
                consistent = True
                for col in ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6', '特码']:
                    if not old_160[col].equals(new_160[col]):
                        consistent = False
                        print(f"警告：{col}列数据不一致")
                
                if consistent:
                    print("✅ 前160期数据与之前保持一致")
                else:
                    print("❌ 前160期数据存在不一致")
            
            # 显示新增的161-170期数据
            new_data = self.df[self.df['期号'] >= 161]
            print(f"\n新增的161-170期数据：")
            print(new_data.to_string(index=False))
            
        except FileNotFoundError:
            print("未找到之前的lottery_data.csv文件")
    
    def analyze_new_data_characteristics(self):
        """分析新数据的特征"""
        print("\n=== 新数据特征分析 ===")
        
        new_data = self.df[self.df['期号'] >= 161]
        
        if len(new_data) == 0:
            print("没有新数据可分析")
            return
        
        print(f"分析161-170期数据特征：")
        
        # 基本统计
        for col in ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6', '特码']:
            values = new_data[col]
            print(f"{col}: 均值={values.mean():.1f}, 标准差={values.std():.1f}, 范围=[{values.min()}-{values.max()}]")
        
        # 和值分析
        new_data_copy = new_data.copy()
        new_data_copy['和值'] = new_data_copy[['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']].sum(axis=1)
        print(f"和值: 均值={new_data_copy['和值'].mean():.1f}, 标准差={new_data_copy['和值'].std():.1f}")
        
        # 奇偶分析
        def count_odd(row):
            return sum(1 for x in [row['数字1'], row['数字2'], row['数字3'], 
                                 row['数字4'], row['数字5'], row['数字6']] if x % 2 == 1)
        
        new_data_copy['奇数个数'] = new_data_copy.apply(count_odd, axis=1)
        print(f"奇数个数: 均值={new_data_copy['奇数个数'].mean():.1f}")
        
        # 大小数分析
        def count_large(row):
            return sum(1 for x in [row['数字1'], row['数字2'], row['数字3'], 
                                 row['数字4'], row['数字5'], row['数字6']] if x > 25)
        
        new_data_copy['大数个数'] = new_data_copy.apply(count_large, axis=1)
        print(f"大数个数: 均值={new_data_copy['大数个数'].mean():.1f}")
    
    def create_data_visualization(self):
        """创建数据可视化"""
        print("\n=== 创建数据可视化 ===")
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 1. 期号分布
        axes[0, 0].hist(self.df['期号'], bins=20, alpha=0.7, color='blue')
        axes[0, 0].axvline(x=150, color='red', linestyle='--', label='训练/测试分界线')
        axes[0, 0].set_title('期号分布')
        axes[0, 0].set_xlabel('期号')
        axes[0, 0].set_ylabel('频次')
        axes[0, 0].legend()
        
        # 2. 数字分布对比（训练集 vs 测试集）
        train_numbers = []
        test_numbers = []
        
        for col in ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']:
            train_numbers.extend(self.train_df[col].tolist())
            test_numbers.extend(self.test_df[col].tolist())
        
        axes[0, 1].hist(train_numbers, bins=49, alpha=0.5, label='训练集(1-150期)', density=True)
        axes[0, 1].hist(test_numbers, bins=49, alpha=0.5, label='测试集(151-170期)', density=True)
        axes[0, 1].set_title('数字分布对比')
        axes[0, 1].set_xlabel('数字')
        axes[0, 1].set_ylabel('密度')
        axes[0, 1].legend()
        
        # 3. 和值趋势
        self.df['和值'] = self.df[['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']].sum(axis=1)
        axes[1, 0].plot(self.df['期号'], self.df['和值'], marker='o', markersize=2)
        axes[1, 0].axvline(x=150, color='red', linestyle='--', label='训练/测试分界线')
        axes[1, 0].set_title('和值趋势')
        axes[1, 0].set_xlabel('期号')
        axes[1, 0].set_ylabel('和值')
        axes[1, 0].legend()
        
        # 4. 新增数据特征
        new_data = self.df[self.df['期号'] >= 161]
        if len(new_data) > 0:
            new_sums = new_data[['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']].sum(axis=1)
            axes[1, 1].bar(new_data['期号'], new_sums, alpha=0.7, color='green')
            axes[1, 1].set_title('新增数据和值分布(161-170期)')
            axes[1, 1].set_xlabel('期号')
            axes[1, 1].set_ylabel('和值')
        
        plt.tight_layout()
        plt.savefig('updated_data_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def run_updated_data_processing(self):
        """运行完整的数据更新处理流程"""
        print("开始数据更新与重新划分")
        print("=" * 50)
        
        # 1. 解析更新后的数据
        self.parse_updated_data()
        
        # 2. 重新划分数据集
        train_df, test_df = self.split_updated_dataset()
        
        # 3. 与之前数据对比
        self.compare_with_previous_data()
        
        # 4. 分析新数据特征
        self.analyze_new_data_characteristics()
        
        # 5. 创建可视化
        self.create_data_visualization()
        
        print("\n" + "=" * 50)
        print("✅ 数据更新与重新划分完成！")
        print(f"📊 总数据量：{len(self.df)}期")
        print(f"🎯 训练集：{len(train_df)}期 (1-150期)")
        print(f"🧪 测试集：{len(test_df)}期 (151-170期)")
        
        return {
            'full_data': self.df,
            'train_data': train_df,
            'test_data': test_df,
            'data_summary': {
                'total_periods': len(self.df),
                'train_periods': len(train_df),
                'test_periods': len(test_df),
                'new_periods': len(self.df[self.df['期号'] >= 161])
            }
        }

if __name__ == "__main__":
    processor = UpdatedDataProcessor()
    results = processor.run_updated_data_processing()
