#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间序列预测方法
尝试ARIMA、Prophet等时间序列预测方法
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.stattools import adfuller
from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class TimeSeriesMethods:
    def __init__(self):
        self.df = pd.read_csv('lottery_data.csv')
        self.train_df = None
        self.test_df = None
        self.arima_models = {}
        self.predictions = {}
        
    def prepare_time_series_data(self):
        """准备时间序列数据"""
        print("=== 准备时间序列数据 ===")
        
        # 按期号排序
        self.df = self.df.sort_values('期号').reset_index(drop=True)
        
        # 划分训练集和测试集
        train_size = 140
        self.train_df = self.df[:train_size].copy()
        self.test_df = self.df[train_size:].copy()
        
        print(f"训练集：{len(self.train_df)}期，测试集：{len(self.test_df)}期")
        
    def check_stationarity(self, series, title):
        """检查时间序列的平稳性"""
        print(f"\n=== {title} 平稳性检验 ===")
        
        # ADF检验
        result = adfuller(series.dropna())
        print(f'ADF统计量: {result[0]:.6f}')
        print(f'p值: {result[1]:.6f}')
        print(f'临界值:')
        for key, value in result[4].items():
            print(f'\t{key}: {value:.3f}')
        
        if result[1] <= 0.05:
            print("序列是平稳的")
            return True
        else:
            print("序列不平稳，需要差分")
            return False
    
    def plot_time_series_analysis(self, series, title):
        """绘制时间序列分析图"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 原始序列
        axes[0, 0].plot(series)
        axes[0, 0].set_title(f'{title} - 原始序列')
        axes[0, 0].set_xlabel('期号')
        axes[0, 0].set_ylabel('数值')
        
        # 一阶差分
        diff_series = series.diff().dropna()
        axes[0, 1].plot(diff_series)
        axes[0, 1].set_title(f'{title} - 一阶差分')
        axes[0, 1].set_xlabel('期号')
        axes[0, 1].set_ylabel('差分值')
        
        # ACF图
        plot_acf(series.dropna(), ax=axes[1, 0], lags=20)
        axes[1, 0].set_title(f'{title} - 自相关函数')
        
        # PACF图
        plot_pacf(series.dropna(), ax=axes[1, 1], lags=20)
        axes[1, 1].set_title(f'{title} - 偏自相关函数')
        
        plt.tight_layout()
        plt.savefig(f'{title}_time_series_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def find_best_arima_params(self, series, max_p=3, max_d=2, max_q=3):
        """寻找最佳ARIMA参数"""
        print(f"\n=== 寻找最佳ARIMA参数 ===")
        
        best_aic = float('inf')
        best_params = None
        best_model = None
        
        for p in range(max_p + 1):
            for d in range(max_d + 1):
                for q in range(max_q + 1):
                    try:
                        model = ARIMA(series, order=(p, d, q))
                        fitted_model = model.fit()
                        aic = fitted_model.aic
                        
                        if aic < best_aic:
                            best_aic = aic
                            best_params = (p, d, q)
                            best_model = fitted_model
                            
                    except Exception as e:
                        continue
        
        print(f"最佳ARIMA参数: {best_params}")
        print(f"最佳AIC值: {best_aic:.2f}")
        
        return best_model, best_params
    
    def train_arima_models(self):
        """训练ARIMA模型"""
        print("\n=== 训练ARIMA模型 ===")
        
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6', '特码']
        
        for col in target_cols:
            print(f"\n训练 {col} 的ARIMA模型...")
            
            # 获取训练数据
            train_series = self.train_df[col]
            
            # 检查平稳性
            is_stationary = self.check_stationarity(train_series, col)
            
            # 绘制时间序列分析图
            self.plot_time_series_analysis(train_series, col)
            
            # 寻找最佳参数并训练模型
            best_model, best_params = self.find_best_arima_params(train_series)
            
            if best_model is not None:
                self.arima_models[col] = {
                    'model': best_model,
                    'params': best_params,
                    'is_stationary': is_stationary
                }
                
                print(f"{col} ARIMA模型训练完成")
                print(f"模型摘要:")
                print(best_model.summary())
            else:
                print(f"{col} ARIMA模型训练失败")
    
    def make_arima_predictions(self):
        """使用ARIMA模型进行预测"""
        print("\n=== ARIMA模型预测 ===")
        
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6', '特码']
        
        for col in target_cols:
            if col in self.arima_models:
                print(f"\n预测 {col}...")
                
                model_info = self.arima_models[col]
                model = model_info['model']
                
                # 预测测试集长度的步数
                forecast_steps = len(self.test_df)
                
                try:
                    # 进行预测
                    forecast = model.forecast(steps=forecast_steps)
                    forecast_ci = model.get_forecast(steps=forecast_steps).conf_int()
                    
                    # 确保预测值在合理范围内
                    forecast = np.clip(forecast, 1, 49)
                    
                    self.predictions[col] = {
                        'forecast': forecast,
                        'confidence_interval': forecast_ci,
                        'actual': self.test_df[col].values
                    }
                    
                    # 计算预测误差
                    mse = np.mean((forecast - self.test_df[col].values) ** 2)
                    mae = np.mean(np.abs(forecast - self.test_df[col].values))
                    
                    print(f"{col} 预测完成")
                    print(f"MSE: {mse:.2f}, MAE: {mae:.2f}")
                    
                except Exception as e:
                    print(f"{col} 预测失败: {str(e)}")
    
    def evaluate_arima_predictions(self):
        """评估ARIMA预测结果"""
        print("\n=== ARIMA预测结果评估 ===")
        
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        # 获取所有预测值
        all_predictions = []
        all_actuals = []
        
        for i in range(len(self.test_df)):
            period_pred = []
            period_actual = []
            
            for col in target_cols:
                if col in self.predictions:
                    forecast_values = self.predictions[col]['forecast']
                    actual_values = self.predictions[col]['actual']

                    pred_val = int(np.round(np.clip(forecast_values.iloc[i] if hasattr(forecast_values, 'iloc') else forecast_values[i], 1, 49)))
                    actual_val = actual_values[i]
                    
                    period_pred.append(pred_val)
                    period_actual.append(actual_val)
            
            if len(period_pred) == 6:  # 确保有完整的6个数字预测
                all_predictions.append(period_pred)
                all_actuals.append(period_actual)
        
        # 计算每期预测正确的数字个数
        correct_per_period = []
        for pred, actual in zip(all_predictions, all_actuals):
            pred_set = set(pred)
            actual_set = set(actual)
            correct_count = len(pred_set.intersection(actual_set))
            correct_per_period.append(correct_count)
        
        if correct_per_period:
            avg_correct = np.mean(correct_per_period)
            max_correct = max(correct_per_period)
            
            print(f"ARIMA模型预测结果：")
            print(f"平均每期预测正确数字个数: {avg_correct:.3f}")
            print(f"最好单期预测: {max_correct} 个数字")
            print(f"预测正确率分布: {np.bincount(correct_per_period)}")
            
            return {
                'avg_correct': avg_correct,
                'max_correct': max_correct,
                'correct_distribution': correct_per_period
            }
        else:
            print("ARIMA预测评估失败：没有有效的预测结果")
            return None
    
    def plot_prediction_results(self):
        """绘制预测结果图"""
        print("\n=== 绘制预测结果 ===")
        
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        axes = axes.flatten()
        
        for i, col in enumerate(target_cols):
            if col in self.predictions:
                pred_info = self.predictions[col]
                
                # 绘制训练数据
                train_x = range(len(self.train_df))
                axes[i].plot(train_x, self.train_df[col], label='训练数据', color='blue')
                
                # 绘制实际测试数据
                test_x = range(len(self.train_df), len(self.train_df) + len(self.test_df))
                axes[i].plot(test_x, pred_info['actual'], label='实际值', color='green', marker='o')
                
                # 绘制预测数据
                axes[i].plot(test_x, pred_info['forecast'], label='ARIMA预测', color='red', marker='s')
                
                axes[i].set_title(f'{col} - ARIMA预测结果')
                axes[i].set_xlabel('期号')
                axes[i].set_ylabel('数值')
                axes[i].legend()
                axes[i].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('arima_prediction_results.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def run_time_series_analysis(self):
        """运行完整的时间序列分析"""
        print("开始时间序列分析")
        print("=" * 50)
        
        # 1. 准备数据
        self.prepare_time_series_data()
        
        # 2. 训练ARIMA模型
        self.train_arima_models()
        
        # 3. 进行预测
        self.make_arima_predictions()
        
        # 4. 评估预测结果
        evaluation_result = self.evaluate_arima_predictions()
        
        # 5. 绘制预测结果
        self.plot_prediction_results()
        
        print("\n" + "=" * 50)
        print("时间序列分析完成！")
        
        return {
            'arima_models': self.arima_models,
            'predictions': self.predictions,
            'evaluation': evaluation_result
        }

if __name__ == "__main__":
    ts_analyzer = TimeSeriesMethods()
    results = ts_analyzer.run_time_series_analysis()
