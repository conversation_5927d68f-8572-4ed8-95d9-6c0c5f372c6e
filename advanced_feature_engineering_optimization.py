#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第一阶段：高级特征工程优化
开发更复杂的时间序列特征、优化特征选择、创建高阶交互特征、引入周期性特征
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.feature_selection import mutual_info_regression, SelectKBest, f_regression
from sklearn.preprocessing import StandardScaler, PolynomialFeatures
from sklearn.decomposition import PCA
from scipy import stats
from scipy.fft import fft, fftfreq
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class AdvancedFeatureEngineering:
    def __init__(self):
        self.df = None
        self.enhanced_df = None
        self.selected_features = {}
        self.feature_importance_scores = {}
        
    def load_data(self):
        """加载数据"""
        print("=== 加载数据 ===")
        self.df = pd.read_csv('updated_lottery_data.csv')
        print(f"数据加载完成：{len(self.df)}期数据")
        return self.df
    
    def create_advanced_time_series_features(self):
        """创建高级时间序列特征"""
        print("\n=== 创建高级时间序列特征 ===")
        
        df = self.df.copy()
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6', '特码']
        
        print("1. 创建多阶差分特征...")
        # 多阶差分特征
        for col in target_cols:
            # 一阶差分
            df[f'{col}_diff1'] = df[col].diff()
            # 二阶差分
            df[f'{col}_diff2'] = df[col].diff().diff()
            # 三阶差分
            df[f'{col}_diff3'] = df[col].diff().diff().diff()
        
        print("2. 创建自相关特征...")
        # 自相关特征
        for col in target_cols:
            for lag in [1, 2, 3, 5, 7, 10]:
                # 滞后相关性
                df[f'{col}_autocorr_lag{lag}'] = df[col].rolling(window=20).apply(
                    lambda x: x.autocorr(lag=lag) if len(x) >= lag + 1 else 0, raw=False)
        
        print("3. 创建趋势和季节性特征...")
        # 趋势特征（多项式拟合）
        for col in target_cols:
            for window in [10, 20, 30]:
                # 线性趋势
                df[f'{col}_linear_trend_{window}'] = df[col].rolling(window=window).apply(
                    lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) == window else 0, raw=False)
                # 二次趋势
                df[f'{col}_quad_trend_{window}'] = df[col].rolling(window=window).apply(
                    lambda x: np.polyfit(range(len(x)), x, 2)[0] if len(x) == window else 0, raw=False)
        
        print("4. 创建波动性和稳定性特征...")
        # 高级波动性特征
        for col in target_cols:
            for window in [5, 10, 20]:
                # 变异系数
                rolling_mean = df[col].rolling(window=window).mean()
                rolling_std = df[col].rolling(window=window).std()
                df[f'{col}_cv_{window}'] = rolling_std / rolling_mean
                
                # 偏度和峰度
                df[f'{col}_skew_{window}'] = df[col].rolling(window=window).skew()
                df[f'{col}_kurtosis_{window}'] = df[col].rolling(window=window).kurt()
                
                # 分位数特征
                df[f'{col}_q25_{window}'] = df[col].rolling(window=window).quantile(0.25)
                df[f'{col}_q75_{window}'] = df[col].rolling(window=window).quantile(0.75)
                df[f'{col}_iqr_{window}'] = (df[f'{col}_q75_{window}'] - df[f'{col}_q25_{window}'])
        
        print("5. 创建频域特征...")
        # 频域特征（基于FFT）
        for col in target_cols:
            window_size = 32  # 使用32期窗口进行FFT
            df[f'{col}_fft_dominant_freq'] = 0.0
            df[f'{col}_fft_power_ratio'] = 0.0
            
            for i in range(window_size, len(df)):
                series = df[col].iloc[i-window_size:i].values
                if len(series) == window_size:
                    # 计算FFT
                    fft_vals = fft(series)
                    freqs = fftfreq(window_size)
                    
                    # 找到主导频率
                    power_spectrum = np.abs(fft_vals)**2
                    dominant_freq_idx = np.argmax(power_spectrum[1:window_size//2]) + 1
                    df.loc[i, f'{col}_fft_dominant_freq'] = freqs[dominant_freq_idx]
                    
                    # 计算功率比
                    total_power = np.sum(power_spectrum)
                    low_freq_power = np.sum(power_spectrum[1:window_size//4])
                    df.loc[i, f'{col}_fft_power_ratio'] = low_freq_power / total_power if total_power > 0 else 0
        
        self.enhanced_df = df
        print(f"高级时间序列特征创建完成，当前特征数：{len(df.columns)}")
        
    def create_cyclical_features(self):
        """创建周期性特征"""
        print("\n=== 创建周期性特征 ===")
        
        df = self.enhanced_df
        
        print("1. 基于期号的周期性特征...")
        # 基于期号的周期性特征
        df['period_sin_7'] = np.sin(2 * np.pi * df['期号'] / 7)  # 7期周期
        df['period_cos_7'] = np.cos(2 * np.pi * df['期号'] / 7)
        df['period_sin_30'] = np.sin(2 * np.pi * df['期号'] / 30)  # 30期周期
        df['period_cos_30'] = np.cos(2 * np.pi * df['期号'] / 30)
        df['period_sin_365'] = np.sin(2 * np.pi * df['期号'] / 365)  # 年周期
        df['period_cos_365'] = np.cos(2 * np.pi * df['期号'] / 365)
        
        print("2. 基于历史模式的周期性特征...")
        # 基于历史重复模式
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        for col in target_cols:
            # 7期前的值（周周期）
            df[f'{col}_cycle_7'] = df[col].shift(7)
            # 30期前的值（月周期）
            df[f'{col}_cycle_30'] = df[col].shift(30)
            
            # 与周期值的差异
            df[f'{col}_diff_cycle_7'] = df[col] - df[f'{col}_cycle_7']
            df[f'{col}_diff_cycle_30'] = df[col] - df[f'{col}_cycle_30']
        
        print("3. 创建组合数字的周期性特征...")
        # 和值的周期性
        df['sum_numbers'] = df[['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']].sum(axis=1)
        df['sum_cycle_7'] = df['sum_numbers'].shift(7)
        df['sum_cycle_30'] = df['sum_numbers'].shift(30)
        df['sum_diff_cycle_7'] = df['sum_numbers'] - df['sum_cycle_7']
        df['sum_diff_cycle_30'] = df['sum_numbers'] - df['sum_cycle_30']
        
        self.enhanced_df = df
        print(f"周期性特征创建完成，当前特征数：{len(df.columns)}")
    
    def create_high_order_interaction_features(self):
        """创建高阶交互特征"""
        print("\n=== 创建高阶交互特征 ===")
        
        df = self.enhanced_df
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        print("1. 创建三元交互特征...")
        # 三元交互特征（选择重要的组合）
        important_combinations = [
            ('数字1', '数字2', '数字3'),
            ('数字4', '数字5', '数字6'),
            ('数字1', '数字3', '数字5'),
            ('数字2', '数字4', '数字6')
        ]
        
        for col1, col2, col3 in important_combinations:
            df[f'{col1}_{col2}_{col3}_product'] = df[col1] * df[col2] * df[col3]
            df[f'{col1}_{col2}_{col3}_sum'] = df[col1] + df[col2] + df[col3]
            df[f'{col1}_{col2}_{col3}_mean'] = (df[col1] + df[col2] + df[col3]) / 3
        
        print("2. 创建多项式特征...")
        # 选择关键特征进行多项式扩展
        key_features = ['sum_numbers', '奇数个数', '大数个数']
        
        for feature in key_features:
            if feature in df.columns:
                df[f'{feature}_squared'] = df[feature] ** 2
                df[f'{feature}_cubed'] = df[feature] ** 3
                df[f'{feature}_sqrt'] = np.sqrt(np.abs(df[feature]))
        
        print("3. 创建比值和差值特征...")
        # 数字间的比值特征
        for i in range(len(target_cols)):
            for j in range(i+1, len(target_cols)):
                col1, col2 = target_cols[i], target_cols[j]
                # 比值特征
                df[f'{col1}_{col2}_ratio'] = df[col1] / (df[col2] + 1e-8)  # 避免除零
                # 差值特征
                df[f'{col1}_{col2}_diff'] = df[col1] - df[col2]
                # 相对差值
                df[f'{col1}_{col2}_rel_diff'] = (df[col1] - df[col2]) / (df[col1] + df[col2] + 1e-8)
        
        print("4. 创建统计交互特征...")
        # 基于统计量的交互特征
        df['max_min_ratio'] = df[target_cols].max(axis=1) / (df[target_cols].min(axis=1) + 1e-8)
        df['range_mean_ratio'] = (df[target_cols].max(axis=1) - df[target_cols].min(axis=1)) / df[target_cols].mean(axis=1)
        df['std_mean_ratio'] = df[target_cols].std(axis=1) / df[target_cols].mean(axis=1)
        
        self.enhanced_df = df
        print(f"高阶交互特征创建完成，当前特征数：{len(df.columns)}")
    
    def optimize_feature_selection(self):
        """优化特征选择算法"""
        print("\n=== 优化特征选择算法 ===")
        
        df = self.enhanced_df.dropna().reset_index(drop=True)
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        # 排除不用于特征选择的列
        exclude_cols = ['期号'] + target_cols + ['特码']
        feature_cols = [col for col in df.columns if col not in exclude_cols]
        
        print(f"可用特征数量：{len(feature_cols)}")
        
        for target_col in target_cols:
            print(f"\n为 {target_col} 优化特征选择...")
            
            X = df[feature_cols].fillna(0)
            y = df[target_col]
            
            # 1. 互信息特征选择
            mi_scores = mutual_info_regression(X, y, random_state=42)
            mi_features = pd.DataFrame({
                'feature': feature_cols,
                'mi_score': mi_scores
            }).sort_values('mi_score', ascending=False)
            
            # 2. F统计量特征选择
            f_scores, f_pvalues = f_regression(X, y)
            f_features = pd.DataFrame({
                'feature': feature_cols,
                'f_score': f_scores,
                'p_value': f_pvalues
            }).sort_values('f_score', ascending=False)
            
            # 3. 相关系数特征选择
            corr_scores = []
            for feature in feature_cols:
                corr = np.corrcoef(X[feature], y)[0, 1]
                corr_scores.append(abs(corr) if not np.isnan(corr) else 0)
            
            corr_features = pd.DataFrame({
                'feature': feature_cols,
                'corr_score': corr_scores
            }).sort_values('corr_score', ascending=False)
            
            # 4. 组合评分（加权平均）
            combined_scores = pd.DataFrame({'feature': feature_cols})
            combined_scores = combined_scores.merge(mi_features[['feature', 'mi_score']], on='feature')
            combined_scores = combined_scores.merge(f_features[['feature', 'f_score']], on='feature')
            combined_scores = combined_scores.merge(corr_features[['feature', 'corr_score']], on='feature')
            
            # 标准化各个评分
            scaler = StandardScaler()
            combined_scores['mi_score_norm'] = scaler.fit_transform(combined_scores[['mi_score']])
            combined_scores['f_score_norm'] = scaler.fit_transform(combined_scores[['f_score']])
            combined_scores['corr_score_norm'] = scaler.fit_transform(combined_scores[['corr_score']])
            
            # 计算组合评分（权重：MI=0.4, F=0.3, Corr=0.3）
            combined_scores['combined_score'] = (
                0.4 * combined_scores['mi_score_norm'] +
                0.3 * combined_scores['f_score_norm'] +
                0.3 * combined_scores['corr_score_norm']
            )
            
            combined_scores = combined_scores.sort_values('combined_score', ascending=False)
            
            # 选择前50个特征
            selected_features = combined_scores.head(50)['feature'].tolist()
            self.selected_features[target_col] = selected_features
            
            # 保存特征重要性评分
            self.feature_importance_scores[target_col] = combined_scores
            
            print(f"  选择的前10个特征：")
            for i, feature in enumerate(selected_features[:10], 1):
                score = combined_scores[combined_scores['feature'] == feature]['combined_score'].iloc[0]
                print(f"    {i}. {feature} (评分: {score:.3f})")
    
    def evaluate_feature_quality(self):
        """评估特征质量"""
        print("\n=== 评估特征质量 ===")
        
        df = self.enhanced_df.dropna().reset_index(drop=True)
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        quality_metrics = {}
        
        for target_col in target_cols:
            selected_features = self.selected_features[target_col]
            X = df[selected_features].fillna(0)
            y = df[target_col]
            
            # 计算特征质量指标
            # 1. 平均互信息
            mi_scores = mutual_info_regression(X, y, random_state=42)
            avg_mi = np.mean(mi_scores)
            
            # 2. 平均相关系数
            corr_scores = []
            for feature in selected_features:
                corr = np.corrcoef(X[feature], y)[0, 1]
                corr_scores.append(abs(corr) if not np.isnan(corr) else 0)
            avg_corr = np.mean(corr_scores)
            
            # 3. 特征间相关性（多重共线性检查）
            feature_corr_matrix = X.corr()
            upper_triangle = np.triu(feature_corr_matrix, k=1)
            high_corr_pairs = np.sum(np.abs(upper_triangle) > 0.8)
            
            quality_metrics[target_col] = {
                'avg_mutual_info': avg_mi,
                'avg_correlation': avg_corr,
                'high_corr_pairs': high_corr_pairs,
                'feature_count': len(selected_features)
            }
            
            print(f"{target_col} 特征质量评估：")
            print(f"  平均互信息: {avg_mi:.4f}")
            print(f"  平均相关系数: {avg_corr:.4f}")
            print(f"  高相关特征对数: {high_corr_pairs}")
            print(f"  选择特征数量: {len(selected_features)}")
        
        return quality_metrics
    
    def create_feature_visualization(self):
        """创建特征可视化"""
        print("\n=== 创建特征可视化 ===")
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        axes = axes.flatten()
        
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        for i, target_col in enumerate(target_cols):
            if target_col in self.feature_importance_scores:
                scores_df = self.feature_importance_scores[target_col].head(15)
                
                # 特征重要性条形图
                axes[i].barh(range(len(scores_df)), scores_df['combined_score'], alpha=0.7)
                axes[i].set_yticks(range(len(scores_df)))
                axes[i].set_yticklabels([f.replace('_', '\n') for f in scores_df['feature']], fontsize=8)
                axes[i].set_xlabel('组合评分')
                axes[i].set_title(f'{target_col} 特征重要性')
                axes[i].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('advanced_feature_importance.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def run_advanced_feature_engineering(self):
        """运行完整的高级特征工程流程"""
        print("开始高级特征工程优化")
        print("=" * 60)
        
        # 1. 加载数据
        self.load_data()
        
        # 2. 创建高级时间序列特征
        self.create_advanced_time_series_features()
        
        # 3. 创建周期性特征
        self.create_cyclical_features()
        
        # 4. 创建高阶交互特征
        self.create_high_order_interaction_features()
        
        # 5. 优化特征选择
        self.optimize_feature_selection()
        
        # 6. 评估特征质量
        quality_metrics = self.evaluate_feature_quality()
        
        # 7. 创建可视化
        self.create_feature_visualization()
        
        # 保存增强后的数据
        self.enhanced_df.to_csv('advanced_enhanced_features.csv', index=False)
        
        print("\n" + "=" * 60)
        print("✅ 高级特征工程优化完成！")
        print(f"📊 原始特征数: {len(self.df.columns)}")
        print(f"🚀 增强后特征数: {len(self.enhanced_df.columns)}")
        print(f"🎯 每个目标选择特征数: 50")
        print(f"💾 增强数据已保存: advanced_enhanced_features.csv")
        
        return {
            'enhanced_df': self.enhanced_df,
            'selected_features': self.selected_features,
            'feature_importance_scores': self.feature_importance_scores,
            'quality_metrics': quality_metrics
        }

if __name__ == "__main__":
    engineer = AdvancedFeatureEngineering()
    results = engineer.run_advanced_feature_engineering()
