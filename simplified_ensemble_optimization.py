#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版高级模型集成优化
专注于传统机器学习模型的集成优化
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, VotingRegressor
from sklearn.neural_network import MLPRegressor
from sklearn.linear_model import Ridge, ElasticNet
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import TimeSeriesSplit, cross_val_score
from sklearn.metrics import mean_squared_error, mean_absolute_error
import xgboost as xgb
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class SimplifiedEnsembleOptimization:
    def __init__(self):
        self.enhanced_df = None
        self.selected_features = None
        self.trained_models = {}
        self.dynamic_weights = {}
        self.ensemble_models = {}
        
    def load_enhanced_data(self):
        """加载增强特征数据"""
        print("=== 加载增强特征数据 ===")
        
        # 加载特征工程结果
        self.enhanced_df = pd.read_csv('advanced_enhanced_features.csv')
        
        # 加载特征选择结果
        from advanced_feature_engineering_optimization import AdvancedFeatureEngineering
        engineer = AdvancedFeatureEngineering()
        engineer.enhanced_df = self.enhanced_df
        engineer.optimize_feature_selection()
        self.selected_features = engineer.selected_features
        
        print(f"增强数据加载完成：{len(self.enhanced_df)}期，{len(self.enhanced_df.columns)}个特征")
        return True
    
    def create_advanced_base_models(self):
        """创建高级基础模型"""
        print("\n=== 创建高级基础模型 ===")
        
        # 优化的机器学习模型
        self.base_models = {
            'RandomForest_Optimized': RandomForestRegressor(
                n_estimators=300,
                max_depth=12,
                min_samples_split=3,
                min_samples_leaf=1,
                max_features='sqrt',
                random_state=42,
                n_jobs=-1
            ),
            'XGBoost_Optimized': xgb.XGBRegressor(
                n_estimators=300,
                max_depth=6,
                learning_rate=0.05,
                subsample=0.9,
                colsample_bytree=0.9,
                reg_alpha=0.1,
                reg_lambda=0.1,
                random_state=42,
                n_jobs=-1
            ),
            'GradientBoosting_Optimized': GradientBoostingRegressor(
                n_estimators=300,
                max_depth=6,
                learning_rate=0.05,
                subsample=0.9,
                max_features='sqrt',
                random_state=42
            ),
            'ElasticNet_Optimized': ElasticNet(
                alpha=0.01,
                l1_ratio=0.7,
                max_iter=2000,
                random_state=42
            ),
            'Ridge_Optimized': Ridge(
                alpha=0.1,
                random_state=42
            ),
            'MLP_Optimized': MLPRegressor(
                hidden_layer_sizes=(150, 100, 50),
                activation='relu',
                solver='adam',
                alpha=0.001,
                learning_rate='adaptive',
                learning_rate_init=0.001,
                max_iter=1000,
                early_stopping=True,
                validation_fraction=0.1,
                n_iter_no_change=20,
                random_state=42
            )
        }
        
        print(f"创建了 {len(self.base_models)} 个优化基础模型")
        
    def train_base_models_with_cv(self):
        """使用交叉验证训练基础模型"""
        print("\n=== 使用交叉验证训练基础模型 ===")
        
        df = self.enhanced_df.dropna().reset_index(drop=True)
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        # 数据分割
        train_df = df[df['期号'] <= 150].copy()
        test_df = df[df['期号'] >= 151].copy()
        
        for target_col in target_cols:
            print(f"\n训练 {target_col} 的模型...")
            
            # 准备数据
            selected_features = self.selected_features[target_col]
            X_train = train_df[selected_features].fillna(0)
            y_train = train_df[target_col]
            X_test = test_df[selected_features].fillna(0)
            y_test = test_df[target_col]
            
            target_models = {}
            
            # 时间序列交叉验证
            tscv = TimeSeriesSplit(n_splits=5)
            
            for model_name, model in self.base_models.items():
                print(f"  训练 {model_name}...")
                
                # 准备数据
                if 'MLP' in model_name:
                    # 神经网络使用标准化数据
                    scaler = StandardScaler()
                    X_train_scaled = scaler.fit_transform(X_train)
                    X_test_scaled = scaler.transform(X_test)
                    
                    # 交叉验证
                    cv_scores = cross_val_score(model, X_train_scaled, y_train, 
                                              cv=tscv, scoring='neg_mean_squared_error')
                    
                    # 训练最终模型
                    model.fit(X_train_scaled, y_train)
                    train_pred = model.predict(X_train_scaled)
                    test_pred = model.predict(X_test_scaled)
                    
                    target_models[model_name] = {
                        'model': model,
                        'scaler': scaler,
                        'cv_scores': cv_scores,
                        'cv_mean': -cv_scores.mean(),
                        'cv_std': cv_scores.std(),
                        'train_mse': mean_squared_error(y_train, train_pred),
                        'test_mse': mean_squared_error(y_test, test_pred),
                        'train_pred': train_pred,
                        'test_pred': test_pred
                    }
                else:
                    # 其他模型使用原始数据
                    cv_scores = cross_val_score(model, X_train, y_train, 
                                              cv=tscv, scoring='neg_mean_squared_error')
                    
                    # 训练最终模型
                    model.fit(X_train, y_train)
                    train_pred = model.predict(X_train)
                    test_pred = model.predict(X_test)
                    
                    target_models[model_name] = {
                        'model': model,
                        'scaler': None,
                        'cv_scores': cv_scores,
                        'cv_mean': -cv_scores.mean(),
                        'cv_std': cv_scores.std(),
                        'train_mse': mean_squared_error(y_train, train_pred),
                        'test_mse': mean_squared_error(y_test, test_pred),
                        'train_pred': train_pred,
                        'test_pred': test_pred
                    }
                
                print(f"    CV MSE: {target_models[model_name]['cv_mean']:.4f} ± {target_models[model_name]['cv_std']:.4f}")
                print(f"    测试MSE: {target_models[model_name]['test_mse']:.4f}")
            
            self.trained_models[target_col] = target_models
        
        return self.trained_models
    
    def implement_dynamic_weighting(self):
        """实施动态权重调整"""
        print("\n=== 实施动态权重调整 ===")
        
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        for target_col in target_cols:
            print(f"\n为 {target_col} 计算动态权重...")
            
            models = self.trained_models[target_col]
            
            # 基于交叉验证和测试性能计算权重
            cv_scores = []
            test_scores = []
            model_names = []
            
            for model_name, model_info in models.items():
                cv_scores.append(model_info['cv_mean'])
                test_scores.append(model_info['test_mse'])
                model_names.append(model_name)
            
            cv_scores = np.array(cv_scores)
            test_scores = np.array(test_scores)
            
            # 计算权重（MSE越小，权重越大）
            cv_weights = 1.0 / (cv_scores + 1e-8)
            test_weights = 1.0 / (test_scores + 1e-8)
            
            # 组合权重（60%基于CV，40%基于测试）
            combined_weights = 0.6 * cv_weights + 0.4 * test_weights
            combined_weights = combined_weights / np.sum(combined_weights)
            
            self.dynamic_weights[target_col] = dict(zip(model_names, combined_weights))
            
            print(f"  动态权重分配：")
            for model_name, weight in self.dynamic_weights[target_col].items():
                print(f"    {model_name}: {weight:.3f}")
    
    def create_stacking_ensemble(self):
        """创建堆叠集成模型"""
        print("\n=== 创建堆叠集成模型 ===")
        
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        for target_col in target_cols:
            print(f"\n为 {target_col} 创建堆叠集成...")
            
            models = self.trained_models[target_col]
            
            # 收集基础模型的预测作为元特征
            train_meta_features = []
            test_meta_features = []
            
            for model_name, model_info in models.items():
                train_meta_features.append(model_info['train_pred'])
                test_meta_features.append(model_info['test_pred'])
            
            # 转换为数组
            X_meta_train = np.column_stack(train_meta_features)
            X_meta_test = np.column_stack(test_meta_features)
            
            # 准备目标值
            df = self.enhanced_df.dropna().reset_index(drop=True)
            train_df = df[df['期号'] <= 150].copy()
            test_df = df[df['期号'] >= 151].copy()
            
            y_train = train_df[target_col]
            y_test = test_df[target_col]
            
            # 训练元学习器
            meta_learner = Ridge(alpha=1.0, random_state=42)
            meta_learner.fit(X_meta_train, y_train)
            
            # 预测
            stacking_train_pred = meta_learner.predict(X_meta_train)
            stacking_test_pred = meta_learner.predict(X_meta_test)
            
            # 计算性能
            train_mse = mean_squared_error(y_train, stacking_train_pred)
            test_mse = mean_squared_error(y_test, stacking_test_pred)
            
            if target_col not in self.ensemble_models:
                self.ensemble_models[target_col] = {}
            
            self.ensemble_models[target_col]['stacking'] = {
                'meta_learner': meta_learner,
                'train_mse': train_mse,
                'test_mse': test_mse,
                'train_pred': stacking_train_pred,
                'test_pred': stacking_test_pred
            }
            
            print(f"  堆叠集成性能 - 训练MSE: {train_mse:.4f}, 测试MSE: {test_mse:.4f}")
    
    def create_weighted_voting_ensemble(self):
        """创建加权投票集成"""
        print("\n=== 创建加权投票集成 ===")
        
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        for target_col in target_cols:
            print(f"\n为 {target_col} 创建加权投票集成...")
            
            models = self.trained_models[target_col]
            weights = list(self.dynamic_weights[target_col].values())
            
            # 使用动态权重进行加权平均
            train_predictions = []
            test_predictions = []
            
            for model_name, model_info in models.items():
                train_predictions.append(model_info['train_pred'])
                test_predictions.append(model_info['test_pred'])
            
            # 加权平均预测
            weighted_train_pred = np.average(train_predictions, axis=0, weights=weights)
            weighted_test_pred = np.average(test_predictions, axis=0, weights=weights)
            
            # 计算性能
            df = self.enhanced_df.dropna().reset_index(drop=True)
            train_df = df[df['期号'] <= 150].copy()
            test_df = df[df['期号'] >= 151].copy()
            
            y_train = train_df[target_col]
            y_test = test_df[target_col]
            
            train_mse = mean_squared_error(y_train, weighted_train_pred)
            test_mse = mean_squared_error(y_test, weighted_test_pred)
            
            if target_col not in self.ensemble_models:
                self.ensemble_models[target_col] = {}
            
            self.ensemble_models[target_col]['weighted_voting'] = {
                'weights': weights,
                'train_mse': train_mse,
                'test_mse': test_mse,
                'train_pred': weighted_train_pred,
                'test_pred': weighted_test_pred
            }
            
            print(f"  加权投票性能 - 训练MSE: {train_mse:.4f}, 测试MSE: {test_mse:.4f}")
    
    def evaluate_ensemble_performance(self):
        """评估集成模型性能"""
        print("\n=== 评估集成模型性能 ===")
        
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        # 准备真实值
        df = self.enhanced_df.dropna().reset_index(drop=True)
        test_df = df[df['期号'] >= 151].copy()
        
        # 选择最佳集成方法
        best_predictions = []
        
        for target_col in target_cols:
            stacking_mse = self.ensemble_models[target_col]['stacking']['test_mse']
            voting_mse = self.ensemble_models[target_col]['weighted_voting']['test_mse']
            
            if stacking_mse <= voting_mse:
                predictions = self.ensemble_models[target_col]['stacking']['test_pred']
                print(f"{target_col}: 使用堆叠集成 (MSE: {stacking_mse:.4f})")
            else:
                predictions = self.ensemble_models[target_col]['weighted_voting']['test_pred']
                print(f"{target_col}: 使用加权投票 (MSE: {voting_mse:.4f})")
            
            best_predictions.append(predictions)
        
        # 转换为数组
        all_predictions = np.array(best_predictions).T  # shape: (n_periods, 6)
        all_actuals = test_df[target_cols].values
        
        # 计算每期正确预测的数字个数
        correct_per_period = []
        for pred, actual in zip(all_predictions, all_actuals):
            # 四舍五入到最近的整数
            pred_rounded = np.round(pred).astype(int)
            # 确保在有效范围内
            pred_rounded = np.clip(pred_rounded, 1, 49)
            
            # 计算正确预测的个数
            pred_set = set(pred_rounded)
            actual_set = set(actual)
            correct_count = len(pred_set.intersection(actual_set))
            correct_per_period.append(correct_count)
        
        # 计算性能指标
        avg_correct = np.mean(correct_per_period)
        max_correct = np.max(correct_per_period)
        std_correct = np.std(correct_per_period)
        
        performance_results = {
            'avg_correct_per_period': avg_correct,
            'max_correct_per_period': max_correct,
            'std_correct_per_period': std_correct,
            'correct_per_period': correct_per_period,
            'total_periods': len(correct_per_period),
            'best_predictions': all_predictions,
            'actuals': all_actuals
        }
        
        print(f"\n集成模型性能评估：")
        print(f"  平均正确预测数字: {avg_correct:.3f} 个/期")
        print(f"  最佳单期表现: {max_correct} 个数字")
        print(f"  标准差: {std_correct:.3f}")
        print(f"  测试期数: {len(correct_per_period)}")
        
        return performance_results
    
    def run_simplified_ensemble_optimization(self):
        """运行简化的集成优化流程"""
        print("开始简化版高级模型集成优化")
        print("=" * 60)
        
        # 1. 加载增强数据
        self.load_enhanced_data()
        
        # 2. 创建高级基础模型
        self.create_advanced_base_models()
        
        # 3. 使用交叉验证训练基础模型
        self.train_base_models_with_cv()
        
        # 4. 实施动态权重调整
        self.implement_dynamic_weighting()
        
        # 5. 创建堆叠集成
        self.create_stacking_ensemble()
        
        # 6. 创建加权投票集成
        self.create_weighted_voting_ensemble()
        
        # 7. 评估集成性能
        self.performance_results = self.evaluate_ensemble_performance()
        
        print("\n" + "=" * 60)
        print("✅ 简化版高级模型集成优化完成！")
        print(f"🎯 平均正确预测: {self.performance_results['avg_correct_per_period']:.3f} 个数字/期")
        print(f"🏆 最佳单期表现: {self.performance_results['max_correct_per_period']} 个数字")
        print(f"📊 基础模型数量: {len(self.base_models)}")
        print(f"🔄 集成方法: 堆叠集成 + 动态权重投票")
        
        return {
            'trained_models': self.trained_models,
            'ensemble_models': self.ensemble_models,
            'dynamic_weights': self.dynamic_weights,
            'performance_results': self.performance_results
        }

if __name__ == "__main__":
    optimizer = SimplifiedEnsembleOptimization()
    results = optimizer.run_simplified_ensemble_optimization()
