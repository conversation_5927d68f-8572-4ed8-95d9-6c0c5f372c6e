#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级特征工程实现
包括特征选择、降维、特征交互、时间序列特征等
"""

import pandas as pd
import numpy as np
from sklearn.feature_selection import SelectKBest, f_regression, mutual_info_regression
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler, PolynomialFeatures
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import cross_val_score
import warnings
warnings.filterwarnings('ignore')

class AdvancedFeatureEngineer:
    def __init__(self):
        self.df = pd.read_csv('lottery_data.csv')
        self.features_df = None
        self.train_df = None
        self.test_df = None
        self.scaler = StandardScaler()
        
    def load_base_features(self):
        """加载基础特征"""
        from lottery_analysis import LotteryAnalyzer
        analyzer = LotteryAnalyzer()
        analyzer.load_and_clean_data()
        self.features_df = analyzer.feature_engineering()
        self.train_df, self.test_df = analyzer.split_dataset()
        
    def create_advanced_time_series_features(self):
        """创建高级时间序列特征"""
        print("=== 创建高级时间序列特征 ===")
        
        if self.features_df is None:
            self.load_base_features()
        
        df = self.features_df.copy()
        
        # 1. 趋势特征
        print("1. 创建趋势特征...")
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6', '特码']
        
        for col in target_cols:
            # 线性趋势
            df[f'{col}_trend_5'] = df[col].rolling(window=5).apply(
                lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) == 5 else 0, raw=False)
            df[f'{col}_trend_10'] = df[col].rolling(window=10).apply(
                lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) == 10 else 0, raw=False)
        
        # 2. 波动性特征
        print("2. 创建波动性特征...")
        for col in target_cols:
            # 变异系数
            df[f'{col}_cv_5'] = df[col].rolling(window=5).std() / df[col].rolling(window=5).mean()
            df[f'{col}_cv_10'] = df[col].rolling(window=10).std() / df[col].rolling(window=10).mean()
            
            # 极值比率
            df[f'{col}_max_min_ratio_5'] = df[col].rolling(window=5).max() / df[col].rolling(window=5).min()
            df[f'{col}_max_min_ratio_10'] = df[col].rolling(window=10).max() / df[col].rolling(window=10).min()
        
        # 3. 周期性特征
        print("3. 创建周期性特征...")
        df['期号_sin_7'] = np.sin(2 * np.pi * df['期号'] / 7)  # 周期为7
        df['期号_cos_7'] = np.cos(2 * np.pi * df['期号'] / 7)
        df['期号_sin_30'] = np.sin(2 * np.pi * df['期号'] / 30)  # 周期为30
        df['期号_cos_30'] = np.cos(2 * np.pi * df['期号'] / 30)
        
        # 4. 序列模式特征
        print("4. 创建序列模式特征...")
        # 上升/下降趋势计数
        for col in target_cols:
            df[f'{col}_up_count_5'] = df[col].rolling(window=6).apply(
                lambda x: sum(x.iloc[i] > x.iloc[i-1] for i in range(1, len(x))) if len(x) == 6 else 0, raw=False)
            df[f'{col}_down_count_5'] = df[col].rolling(window=6).apply(
                lambda x: sum(x.iloc[i] < x.iloc[i-1] for i in range(1, len(x))) if len(x) == 6 else 0, raw=False)
        
        self.features_df = df
        print(f"高级时间序列特征创建完成，新增特征数量：{len(df.columns) - len(self.features_df.columns) if hasattr(self, 'original_feature_count') else 'N/A'}")
        
        return df
    
    def create_interaction_features(self):
        """创建特征交互"""
        print("\n=== 创建特征交互 ===")
        
        if self.features_df is None:
            self.create_advanced_time_series_features()
        
        df = self.features_df.copy()
        
        # 选择重要的基础特征进行交互
        important_features = [
            '和值', '和值_mean_5', '和值_std_5', 
            '奇数个数_mean_5', '大数个数_mean_5',
            '连号个数', '重复号个数'
        ]
        
        # 1. 二阶交互特征
        print("1. 创建二阶交互特征...")
        for i, feat1 in enumerate(important_features):
            for feat2 in important_features[i+1:]:
                if feat1 in df.columns and feat2 in df.columns:
                    # 乘积交互
                    df[f'{feat1}_x_{feat2}'] = df[feat1] * df[feat2]
                    # 比率交互
                    df[f'{feat1}_div_{feat2}'] = df[feat1] / (df[feat2] + 1e-8)
        
        # 2. 滞后特征交互
        print("2. 创建滞后特征交互...")
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        for i, col1 in enumerate(target_cols):
            for col2 in target_cols[i+1:]:
                if f'{col1}_lag1' in df.columns and f'{col2}_lag1' in df.columns:
                    df[f'{col1}_{col2}_lag_interaction'] = df[f'{col1}_lag1'] * df[f'{col2}_lag1']
        
        # 3. 统计特征组合
        print("3. 创建统计特征组合...")
        df['和值_奇偶_组合'] = df['和值'] * df['奇数个数_mean_5']
        df['和值_大小_组合'] = df['和值'] * df['大数个数_mean_5']
        df['波动_连号_组合'] = df['和值_std_5'] * df['连号个数']
        
        self.features_df = df
        print(f"特征交互创建完成，当前总特征数：{len(df.columns)}")
        
        return df
    
    def feature_selection(self, method='mutual_info', k=50):
        """特征选择"""
        print(f"\n=== 特征选择 (方法: {method}, 选择: {k}个特征) ===")

        if self.features_df is None:
            self.create_interaction_features()

        # 重新划分数据集以包含新特征
        train_mask = self.features_df['期号'] <= 140
        test_mask = self.features_df['期号'] > 140

        self.train_df = self.features_df[train_mask].dropna()
        self.test_df = self.features_df[test_mask].dropna()

        # 准备数据
        exclude_cols = ['期号', '数字1', '数字2', '数字3', '数字4', '数字5', '数字6', '特码']
        feature_cols = [col for col in self.features_df.columns if col not in exclude_cols]

        X_train = self.train_df[feature_cols].fillna(0)
        
        selected_features_dict = {}
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        for target_col in target_cols:
            y_train = self.train_df[target_col]
            
            if method == 'mutual_info':
                selector = SelectKBest(score_func=mutual_info_regression, k=min(k, len(feature_cols)))
            elif method == 'f_regression':
                selector = SelectKBest(score_func=f_regression, k=min(k, len(feature_cols)))
            else:  # random_forest
                rf = RandomForestRegressor(n_estimators=50, random_state=42)
                rf.fit(X_train, y_train)
                feature_importance = pd.DataFrame({
                    'feature': feature_cols,
                    'importance': rf.feature_importances_
                }).sort_values('importance', ascending=False)
                selected_features_dict[target_col] = feature_importance.head(k)['feature'].tolist()
                continue
            
            X_selected = selector.fit_transform(X_train, y_train)
            selected_features = [feature_cols[i] for i in selector.get_support(indices=True)]
            selected_features_dict[target_col] = selected_features
            
            print(f"{target_col} 选择的前10个特征：")
            for i, feat in enumerate(selected_features[:10]):
                print(f"  {i+1}. {feat}")
        
        return selected_features_dict
    
    def dimensionality_reduction(self, n_components=20):
        """降维处理"""
        print(f"\n=== 降维处理 (PCA, 保留{n_components}个主成分) ===")
        
        if self.features_df is None:
            self.create_interaction_features()
        
        # 准备数据
        exclude_cols = ['期号', '数字1', '数字2', '数字3', '数字4', '数字5', '数字6', '特码']
        feature_cols = [col for col in self.features_df.columns if col not in exclude_cols]
        
        X_train = self.train_df[feature_cols].fillna(0)
        X_test = self.test_df[feature_cols].fillna(0)
        
        # 标准化
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # PCA降维
        pca = PCA(n_components=n_components)
        X_train_pca = pca.fit_transform(X_train_scaled)
        X_test_pca = pca.transform(X_test_scaled)
        
        # 创建PCA特征DataFrame
        pca_columns = [f'PCA_{i+1}' for i in range(n_components)]
        train_pca_df = pd.DataFrame(X_train_pca, columns=pca_columns, index=self.train_df.index)
        test_pca_df = pd.DataFrame(X_test_pca, columns=pca_columns, index=self.test_df.index)
        
        print(f"PCA解释方差比例：{pca.explained_variance_ratio_[:5]}")
        print(f"前{n_components}个主成分累计解释方差：{sum(pca.explained_variance_ratio_):.3f}")
        
        return train_pca_df, test_pca_df, pca
    
    def create_ensemble_features(self):
        """创建集成特征（简化版）"""
        print("\n=== 创建集成特征 ===")

        if self.features_df is None:
            self.create_interaction_features()

        df = self.features_df.copy()

        # 1. 简单的统计组合特征
        print("1. 创建统计组合特征...")
        df['和值_变异系数'] = df['和值_std_5'] / (df['和值_mean_5'] + 1e-8)
        df['奇偶_平衡度'] = abs(df['奇数个数_mean_5'] - 3)  # 距离理想平衡的偏差
        df['大小_平衡度'] = abs(df['大数个数_mean_5'] - 3)  # 距离理想平衡的偏差

        # 2. 周期性特征
        print("2. 创建周期性特征...")
        df['期号_mod_7'] = df['期号'] % 7  # 周期为7
        df['期号_mod_10'] = df['期号'] % 10  # 周期为10

        self.features_df = df
        print(f"集成特征创建完成，当前总特征数：{len(df.columns)}")

        return df
    
    def run_advanced_feature_engineering(self):
        """运行完整的高级特征工程流程"""
        print("开始高级特征工程流程")
        print("=" * 50)

        # 1. 创建高级时间序列特征
        enhanced_df = self.create_advanced_time_series_features()

        # 2. 创建特征交互
        enhanced_df = self.create_interaction_features()

        # 3. 创建集成特征
        enhanced_df = self.create_ensemble_features()

        # 重新划分数据集
        train_mask = enhanced_df['期号'] <= 140
        test_mask = enhanced_df['期号'] > 140

        self.train_df = enhanced_df[train_mask].dropna()
        self.test_df = enhanced_df[test_mask].dropna()

        print(f"重新划分后 - 训练集：{len(self.train_df)}期，测试集：{len(self.test_df)}期")

        # 4. 特征选择
        selected_features = self.feature_selection(method='mutual_info', k=30)

        # 5. 降维处理
        train_pca, test_pca, pca = self.dimensionality_reduction(n_components=15)

        print("\n" + "=" * 50)
        print("高级特征工程完成！")
        print(f"最终特征数量：{len(enhanced_df.columns)}")

        return {
            'features_df': enhanced_df,
            'train_df': self.train_df,
            'test_df': self.test_df,
            'selected_features': selected_features,
            'pca_features': (train_pca, test_pca),
            'pca_model': pca
        }

if __name__ == "__main__":
    engineer = AdvancedFeatureEngineer()
    results = engineer.run_advanced_feature_engineering()
