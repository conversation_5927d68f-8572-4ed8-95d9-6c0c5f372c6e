# 彩票数据分析项目

这是一个完整的彩票数据分析项目，按照数据科学的标准流程进行，包含数据清洗、探索性数据分析、特征工程、模型训练和评估等步骤。

## 项目结构

```
CP_vs/
├── 数据集.txt                    # 原始彩票数据
├── lottery_data.csv              # 清洗后的结构化数据
├── lottery_analysis.py           # 数据清洗和EDA主程序
├── lottery_models.py             # 机器学习模型实现
├── lottery_analysis_report.py    # 综合分析报告
├── lottery_eda_analysis.png      # EDA可视化图表
├── lottery_comprehensive_analysis.png  # 综合分析图表
└── README.md                     # 项目说明文档
```

## 项目步骤

### 步骤1：数据准备与清洗 ✅
- 将原始文本数据解析为结构化的CSV格式
- 包含期号、6个正常数字和特码共8列
- 数据范围：第1期到第160期，共160期数据

### 步骤2：探索性数据分析(EDA) ✅
- **冷热号分析**：最热门数字45(33次)，最冷门数字36(13次)
- **奇偶比分析**：奇数51.4%，偶数48.6%，分布相对均匀
- **大小比分析**：大数(>25)和小数(≤25)各占50%，完全均匀
- **和值分布**：范围66-238，均值151.6，标准差32.8
- **频率变异系数**：0.213，说明数字分布相对均匀

### 步骤3：特征工程 ✅
创建了107个特征，包括：
- **滞后特征**：前1、2、3、5、10期的历史数据
- **滑动窗口统计特征**：5、10、20期的和值、奇偶比、大小比统计
- **间隔特征**：每个数字距离上次出现的时间间隔
- **组合特征**：连号个数、重复号个数等

### 步骤4：数据集划分 ✅
- **训练集**：第1-140期（移除NaN后121期）
- **测试集**：第141-160期（20期）
- 严格按时间顺序划分，避免数据泄露

### 步骤5：模型训练与评估 ✅
实现了随机森林模型：
- 为每个数字位置训练独立的回归模型
- 使用100棵决策树，最大深度10
- 特征重要性分析显示"和值"是最重要的特征

### 步骤6：模型性能评估 ✅
**结果分析**：
- 随机森林：平均每期预测对 0.20 个数字
- 随机基准：平均每期预测对 0.40 个数字
- **模型表现不如随机基准**

## 关键发现

### 1. 数据特征
- 数字分布相对均匀，符合彩票的随机性设计
- 卡方统计量44.81，说明存在一定的不均匀性
- 平均连号个数0.60，重复号个数0.81

### 2. 预测难度
- 理论随机命中概率：单个数字12.2%
- 彩票具有很强的随机性，历史模式难以预测未来
- 160期数据量相对较少，不足以学习复杂模式

### 3. 模型局限性
- 传统机器学习模型在彩票预测上效果有限
- 特征工程虽然全面，但可能无法捕捉真正的预测信号
- 模型可能过拟合了训练数据中的噪声

## 运行方法

1. **数据清洗和EDA**：
```bash
python lottery_analysis.py
```

2. **模型训练和评估**：
```bash
python lottery_models.py
```

3. **生成综合报告**：
```bash
python lottery_analysis_report.py
```

## 改进建议

### A. 特征工程改进
1. 尝试更复杂的组合特征
2. 考虑季节性、周期性特征
3. 引入外部因素（日期、节假日等）
4. 使用更长的历史窗口

### B. 模型改进
1. 尝试集成学习方法
2. 使用深度学习模型（LSTM、Transformer）
3. 考虑概率预测而非点预测
4. 使用贝叶斯方法处理不确定性

### C. 评估方法改进
1. 使用更多样的评估指标
2. 考虑部分匹配的奖励机制
3. 分析预测的置信度
4. 进行更长期的回测

## 重要声明

⚠️ **风险提示**：
1. 彩票具有很强的随机性，AI预测效果有限
2. 任何预测模型都不能保证盈利
3. 理性对待彩票，不要过度依赖预测
4. 本项目仅作为数据科学学习和研究用途

## 技术栈

- **Python 3.x**
- **数据处理**：pandas, numpy
- **可视化**：matplotlib, seaborn
- **机器学习**：scikit-learn
- **深度学习**：tensorflow/keras

## 结论

通过完整的数据科学流程分析，我们发现：

1. **数据质量良好**：160期数据结构完整，分布相对均匀
2. **特征工程全面**：创建了多维度的107个特征
3. **模型实现正确**：随机森林模型训练和评估流程规范
4. **结果符合预期**：彩票的随机性使得预测极其困难

这个项目很好地展示了数据科学的完整流程，虽然在彩票预测上效果有限，但作为学习项目具有很高的价值。它证明了即使使用先进的机器学习技术，面对真正随机的数据时，预测能力仍然有限。

## 许可证

本项目仅供学习和研究使用。
