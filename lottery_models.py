#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
彩票预测模型实现
包含随机森林和LSTM模型的训练与评估
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from tensorflow.keras.optimizers import Adam
import random
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class LotteryPredictor:
    def __init__(self):
        self.train_df = None
        self.test_df = None
        self.rf_models = {}  # 存储每个数字位置的随机森林模型
        self.lstm_models = {}  # 存储每个数字位置的LSTM模型
        self.scalers = {}  # 存储标准化器
        
    def load_data(self):
        """加载处理好的数据"""
        # 重新运行数据处理流程
        from lottery_analysis import LotteryAnalyzer
        analyzer = LotteryAnalyzer()
        analyzer.load_and_clean_data()
        analyzer.feature_engineering()
        train_df, test_df = analyzer.split_dataset()
        
        self.train_df = train_df
        self.test_df = test_df
        
        print(f"训练集大小：{len(self.train_df)}")
        print(f"测试集大小：{len(self.test_df)}")
        
        return train_df, test_df
    
    def prepare_features(self, df):
        """准备特征数据"""
        # 选择重要的特征（排除目标变量和一些不相关的特征）
        exclude_cols = ['期号', '数字1', '数字2', '数字3', '数字4', '数字5', '数字6', '特码']
        feature_cols = [col for col in df.columns if col not in exclude_cols]
        
        # 进一步筛选特征，避免过多的间隔特征
        selected_features = []
        for col in feature_cols:
            if '间隔' in col:
                # 只保留一些重要数字的间隔特征
                important_numbers = [1, 3, 5, 10, 15, 20, 25, 30, 35, 40, 45]
                if any(f'数字{num}_间隔' == col for num in important_numbers):
                    selected_features.append(col)
            else:
                selected_features.append(col)
        
        return selected_features
    
    def train_random_forest(self):
        """训练随机森林模型"""
        print("\n=== 训练随机森林模型 ===")
        
        if self.train_df is None:
            self.load_data()
        
        feature_cols = self.prepare_features(self.train_df)
        X_train = self.train_df[feature_cols].fillna(0)
        
        # 为每个数字位置训练一个模型
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6', '特码']
        
        for target_col in target_cols:
            print(f"训练 {target_col} 的随机森林模型...")
            
            y_train = self.train_df[target_col]
            
            # 创建和训练随机森林模型
            rf_model = RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                n_jobs=-1
            )
            
            rf_model.fit(X_train, y_train)
            self.rf_models[target_col] = rf_model
            
            # 输出特征重要性
            feature_importance = pd.DataFrame({
                'feature': feature_cols,
                'importance': rf_model.feature_importances_
            }).sort_values('importance', ascending=False)
            
            print(f"{target_col} 前5个重要特征：")
            print(feature_importance.head())
        
        print("随机森林模型训练完成！")
    
    def train_lstm(self):
        """训练LSTM模型"""
        print("\n=== 训练LSTM模型 ===")
        
        if self.train_df is None:
            self.load_data()
        
        # 准备LSTM的序列数据
        sequence_length = 10  # 使用前10期的数据预测下一期
        
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6', '特码']
        
        for target_col in target_cols:
            print(f"训练 {target_col} 的LSTM模型...")
            
            # 准备序列数据
            data = self.train_df[target_col].values
            
            X_sequences = []
            y_sequences = []
            
            for i in range(sequence_length, len(data)):
                X_sequences.append(data[i-sequence_length:i])
                y_sequences.append(data[i])
            
            X_sequences = np.array(X_sequences)
            y_sequences = np.array(y_sequences)
            
            # 重塑数据为LSTM输入格式
            X_sequences = X_sequences.reshape((X_sequences.shape[0], X_sequences.shape[1], 1))
            
            # 标准化
            scaler = StandardScaler()
            X_sequences_scaled = scaler.fit_transform(X_sequences.reshape(-1, 1)).reshape(X_sequences.shape)
            y_sequences_scaled = scaler.fit_transform(y_sequences.reshape(-1, 1)).flatten()
            
            self.scalers[target_col] = scaler
            
            # 创建LSTM模型
            model = Sequential([
                LSTM(50, return_sequences=True, input_shape=(sequence_length, 1)),
                Dropout(0.2),
                LSTM(50, return_sequences=False),
                Dropout(0.2),
                Dense(25),
                Dense(1)
            ])
            
            model.compile(optimizer=Adam(learning_rate=0.001), loss='mse')
            
            # 训练模型
            history = model.fit(
                X_sequences_scaled, y_sequences_scaled,
                epochs=50,
                batch_size=16,
                validation_split=0.2,
                verbose=0
            )
            
            self.lstm_models[target_col] = model
        
        print("LSTM模型训练完成！")
    
    def evaluate_models(self):
        """评估模型性能"""
        print("\n=== 模型评估 ===")
        
        if not self.rf_models and not self.lstm_models:
            print("请先训练模型")
            return
        
        feature_cols = self.prepare_features(self.test_df)
        X_test = self.test_df[feature_cols].fillna(0)
        
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6', '特码']
        
        # 存储预测结果
        rf_predictions = {}
        lstm_predictions = {}
        actual_values = {}
        
        # 随机森林预测
        if self.rf_models:
            print("随机森林预测结果：")
            for target_col in target_cols:
                if target_col in self.rf_models:
                    y_pred = self.rf_models[target_col].predict(X_test)
                    y_true = self.test_df[target_col].values
                    
                    # 将预测值四舍五入到最近的整数
                    y_pred_rounded = np.round(y_pred).astype(int)
                    # 确保预测值在合理范围内（1-49）
                    y_pred_rounded = np.clip(y_pred_rounded, 1, 49)
                    
                    rf_predictions[target_col] = y_pred_rounded
                    actual_values[target_col] = y_true
                    
                    # 计算准确率（完全匹配）
                    accuracy = np.mean(y_pred_rounded == y_true)
                    mse = mean_squared_error(y_true, y_pred_rounded)
                    
                    print(f"{target_col}: 准确率={accuracy:.3f}, MSE={mse:.2f}")
        
        # LSTM预测
        if self.lstm_models:
            print("\nLSTM预测结果：")
            sequence_length = 10
            
            for target_col in target_cols:
                if target_col in self.lstm_models:
                    # 准备测试序列
                    all_data = pd.concat([self.train_df, self.test_df])[target_col].values
                    
                    predictions = []
                    for i in range(len(self.train_df), len(all_data)):
                        if i >= sequence_length:
                            sequence = all_data[i-sequence_length:i]
                            sequence_scaled = self.scalers[target_col].transform(sequence.reshape(-1, 1))
                            sequence_scaled = sequence_scaled.reshape(1, sequence_length, 1)
                            
                            pred_scaled = self.lstm_models[target_col].predict(sequence_scaled, verbose=0)
                            pred = self.scalers[target_col].inverse_transform(pred_scaled)
                            predictions.append(pred[0, 0])
                    
                    if predictions:
                        y_pred = np.array(predictions)
                        y_true = self.test_df[target_col].values[:len(predictions)]
                        
                        # 将预测值四舍五入到最近的整数
                        y_pred_rounded = np.round(y_pred).astype(int)
                        # 确保预测值在合理范围内（1-49）
                        y_pred_rounded = np.clip(y_pred_rounded, 1, 49)
                        
                        lstm_predictions[target_col] = y_pred_rounded
                        
                        # 计算准确率
                        accuracy = np.mean(y_pred_rounded == y_true)
                        mse = mean_squared_error(y_true, y_pred_rounded)
                        
                        print(f"{target_col}: 准确率={accuracy:.3f}, MSE={mse:.2f}")
        
        # 计算整体预测性能
        self._evaluate_overall_performance(rf_predictions, lstm_predictions, actual_values)
        
        return rf_predictions, lstm_predictions, actual_values
    
    def _evaluate_overall_performance(self, rf_predictions, lstm_predictions, actual_values):
        """评估整体预测性能"""
        print("\n=== 整体预测性能评估 ===")
        
        # 计算每期能预测对几个数字
        if rf_predictions:
            rf_correct_per_period = []
            for i in range(len(self.test_df)):
                correct_count = 0
                for col in ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']:
                    if col in rf_predictions and i < len(rf_predictions[col]):
                        if rf_predictions[col][i] == actual_values[col][i]:
                            correct_count += 1
                rf_correct_per_period.append(correct_count)
            
            avg_correct_rf = np.mean(rf_correct_per_period)
            print(f"随机森林：平均每期预测对 {avg_correct_rf:.2f} 个数字")
        
        if lstm_predictions:
            lstm_correct_per_period = []
            for i in range(len(self.test_df)):
                correct_count = 0
                for col in ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']:
                    if col in lstm_predictions and i < len(lstm_predictions[col]):
                        if lstm_predictions[col][i] == actual_values[col][i]:
                            correct_count += 1
                lstm_correct_per_period.append(correct_count)
            
            avg_correct_lstm = np.mean(lstm_correct_per_period)
            print(f"LSTM：平均每期预测对 {avg_correct_lstm:.2f} 个数字")
        
        # 随机基准比较
        random_correct_per_period = []
        for i in range(len(self.test_df)):
            # 随机选择6个数字
            random_numbers = random.sample(range(1, 50), 6)
            actual_numbers = [actual_values[col][i] for col in ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']]
            correct_count = len(set(random_numbers).intersection(set(actual_numbers)))
            random_correct_per_period.append(correct_count)
        
        avg_correct_random = np.mean(random_correct_per_period)
        print(f"随机基准：平均每期预测对 {avg_correct_random:.2f} 个数字")

if __name__ == "__main__":
    # 创建预测器实例
    predictor = LotteryPredictor()
    
    # 加载数据
    predictor.load_data()
    
    # 训练随机森林模型
    predictor.train_random_forest()
    
    # 训练LSTM模型（可选，计算量较大）
    # predictor.train_lstm()
    
    # 评估模型
    predictor.evaluate_models()
